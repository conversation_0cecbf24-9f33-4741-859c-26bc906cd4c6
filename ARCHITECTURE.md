## Tabla de Contenido

[1. Resumen Ejecutivo](#1-resumen-ejecutivo)

[2. Diagrama C4 - Vista de Containers](#2-diagrama-c4---vista-de-containers)

[3. Estructura de Módulos y Responsabilidades](#3-estructura-de-módulos-y-responsabilidades)

[4. <PERSON><PERSON><PERSON> de Datos entre Componentes](#4-flujo-de-datos-entre-componentes)

[5. Registro de Decisiones Arquitectónicas (ADR)](#5-registro-de-decisiones-arquitectónicas-adr)

[6. Diagramas de Secuencia](#6-diagramas-de-secuencia)

[7. Referencias Cruzadas](#7-referencias-cruzadas)

[8. Checklist de Validación](#8-checklist-de-validación)

[9. <PERSON>losario](#9-glosario)

[10. Apéndices](#10-apéndices)


## 1. Resumen Ejecutivo

#### La aplicación móvil **Agenda Familiar** implementa una arquitectura híbrida que combina principios de **Domain-Driven Design (DDD)**, **Arquitectura Hexagonal** y **Atomic Design** para crear una solución escalable, mantenible y testeable.

### Características Principales

- **Stack Tecnológico**: Ionic 8 + React 19 + TypeScript 5 + Capacitor 7
- **Patrón Arquitectónico**: Hexagonal con separación clara de responsabilidades
- **Estructura UI**: Atomic Design (átomos → moléculas → organismos → templates)
- **Gestión de Estado**: React Hooks + Context API con servicios de dominio
- **Integración Externa**: OpenID Connect (PKCE) + APIs RESTful del ecosistema Santillana

### Decisiones Clave

1. **Separación por dominios** siguiendo DDD para alta cohesión y bajo acoplamiento
2. **Integración WebView** para pagos manteniendo seguridad y consistencia UX
3. **Caché híbrida** para soporte offline parcial
4. **Testing por capas** con enfoque en pirámide de testing (unitario → integración → e2e)

### Beneficios Arquitectónicos

- **Mantenibilidad**: Módulos independientes y responsabilidades bien definidas
- **Escalabilidad**: Estructura preparada para nuevos dominios y funcionalidades
- **Testabilidad**: Separación de lógica de negocio permite testing granular
- **Integración**: Adaptadores permiten cambios en servicios externos sin impacto

## 2. Diagrama C4 - Vista de Containers

```mermaid
graph TB
    subgraph "Dispositivo Móvil"
        AF[Agenda Familiar App<br/>Ionic/React]
        WV[WebView Pagos<br/>iframe integrado]
    end
    
    subgraph "Santillana Connect - Autenticación"
        SC[Santillana Connect<br/>OpenID Provider]
        SCIM[API SCIM<br/>Gestión Usuarios]
    end
    
    subgraph "APIs Santillana"
        CAL[API Calendario<br/>Eventos académicos]
        MSG[API Mensajería<br/>Comunicación]
        AST[API Asistencia<br/>Registro/Justificación]
        INF[API Informes<br/>Progreso académico]
        REC[API Recursos<br/>Contenido educativo]
    end
    
    subgraph "Sistemas Externos"
        LMS[LMS Tangerine<br/>Tareas y actividades]
        EVAL[Sistema Evaluación<br/>Pleno]
        PAY[Sistema Pagos<br/>Gateway]
        EDI[Ecosistema Digital<br/>EDI]
    end
    
    subgraph "Almacenamiento Local"
        CACHE[Cache Local<br/>Ionic Storage]
        SEC[Secure Storage<br/>Tokens/Credenciales]
    end
    
    %% Conexiones principales
    AF -->|OpenID Connect<br/>PKCE| SC
    AF -->|Bearer Token| SCIM
    AF -->|HTTP/REST| CAL
    AF -->|HTTP/REST| MSG
    AF -->|HTTP/REST| AST
    AF -->|HTTP/REST| INF
    AF -->|HTTP/REST| REC
    AF -->|postMessage| WV
    WV -->|HTTPS| PAY
    
    %% Integraciones backend
    CAL --> LMS
    CAL --> EVAL
    INF --> LMS
    INF --> EVAL
    
    %% Almacenamiento
    AF --> CACHE
    AF --> SEC
    
    %% Estilos
    classDef mobile fill:#e1f5fe
    classDef auth fill:#f3e5f5
    classDef api fill:#e8f5e8
    classDef external fill:#fff3e0
    classDef storage fill:#fce4ec
    
    class AF,WV mobile
    class SC,SCIM auth
    class CAL,MSG,AST,INF,REC api
    class LMS,EVAL,PAY,EDI external
    class CACHE,SEC storage
```

## 3. Estructura de Módulos y Responsabilidades

### 3.1 Vista General de Capas

```mermaid
graph TB
    subgraph "Capa de Presentación"
        UI[UI Components<br/>Atomic Design]
        PAGES[Pages<br/>Rutas de aplicación]
    end
    
    subgraph "Capa de Aplicación"
        HOOKS[Custom Hooks<br/>Estado y lógica UI]
        ROUTES[Route Guards<br/>Protección rutas]
    end
    
    subgraph "Capa de Dominio"
        ENTITIES[Entities<br/>Modelos de negocio]
        SERVICES[Domain Services<br/>Lógica de negocio]
        REPOS[Repository Interfaces<br/>Contratos de datos]
    end
    
    subgraph "Capa de Infraestructura"
        ADAPTERS[API Adapters<br/>Implementaciones]
        STORAGE[Storage Adapters<br/>Cache/Persistencia]
        DEVICE[Device Adapters<br/>Capacidades nativas]
    end
    
    subgraph "Capa Transversal"
        CORE[Core Services<br/>Auth, HTTP, Config]
        UTILS[Utilities<br/>Helpers compartidos]
    end
    
    %% Dependencias
    UI --> HOOKS
    PAGES --> HOOKS
    HOOKS --> SERVICES
    ROUTES --> SERVICES
    SERVICES --> REPOS
    SERVICES --> ENTITIES
    ADAPTERS -.-> REPOS
    STORAGE -.-> REPOS
    DEVICE -.-> REPOS
    HOOKS --> CORE
    SERVICES --> CORE
    ADAPTERS --> CORE
    
    classDef presentation fill:#e3f2fd
    classDef application fill:#f1f8e9
    classDef domain fill:#fff3e0
    classDef infrastructure fill:#fce4ec
    classDef cross fill:#f3e5f5
    
    class UI,PAGES presentation
    class HOOKS,ROUTES application
    class ENTITIES,SERVICES,REPOS domain
    class ADAPTERS,STORAGE,DEVICE infrastructure
    class CORE,UTILS cross
```

### 3.2 Responsabilidades por Módulo

| Módulo             | Responsabilidad Principal                                                   | Dependencias            |
|--------------------|-----------------------------------------------------------------------------|-------------------------|
| **core/**          | Funcionalidad transversal: auth, HTTP, storage, configuración               | Ninguna (base)          |
| **domains/**       | Lógica de negocio pura, entidades, contratos de repositorio                 | core/                   |
| **infrastructure/**| Implementaciones de integraciones externas                                  | domains/, core/         |
| **ui/**            | Componentes de interfaz siguiendo Atomic Design                             | domains/ (hooks)        |
| **pages/**         | Páginas de aplicación que orquestan componentes UI                          | ui/, domains/           |
| **routes/**        | Configuración de navegación y protección de rutas                           | core/auth/, pages/      |
| **utils/**         | Utilidades compartidas sin lógica de negocio                                | Ninguna                 |
| **tests/**         | Pruebas de todos los niveles                                                | Todos los módulos       |

## 4. Flujo de Datos entre Componentes

### 4.1 Flujo General de Datos

```mermaid
graph TD
    subgraph "Capa de Presentación"
        P1[Page Component]
        U1[UI Organism]
        U2[UI Molecule]
        U3[UI Atom]
    end
    
    subgraph "Capa de Aplicación"
        H1[Custom Hook]
        H2[Domain Hook]
    end
    
    subgraph "Capa de Dominio"
        S1[Domain Service]
        E1[Entity]
        R1[Repository Interface]
    end
    
    subgraph "Capa de Infraestructura"
        A1[API Adapter]
        S2[Storage Adapter]
        M1[API Mapper]
    end
    
    subgraph "Servicios Externos"
        API[External API]
        DB[Local Storage]
    end
    
    %% Flujo de datos hacia abajo
    P1 --> U1
    U1 --> U2
    U2 --> U3
    P1 --> H1
    U1 --> H2
    H1 --> S1
    H2 --> S1
    S1 --> E1
    S1 --> R1
    A1 -.-> R1
    S2 -.-> R1
    A1 --> M1
    A1 --> API
    S2 --> DB
    
    %% Flujo de datos hacia arriba
    API --> A1
    DB --> S2
    M1 --> A1
    R1 --> S1
    E1 --> S1
    S1 --> H2
    S1 --> H1
    H2 --> U1
    H1 --> P1
    
    classDef presentation fill:#e3f2fd
    classDef application fill:#f1f8e9
    classDef domain fill:#fff3e0
    classDef infrastructure fill:#fce4ec
    classDef external fill:#f3e5f5
    
    class P1,U1,U2,U3 presentation
    class H1,H2 application
    class S1,E1,R1 domain
    class A1,S2,M1 infrastructure
    class API,DB external
```

### 4.2 Ejemplo Detallado: Flujo de Calendario

```mermaid
sequenceDiagram
    participant CP as CalendarPage
    participant UC as useCalendar Hook
    participant CS as CalendarService
    participant CR as CalendarRepository
    participant CAC as CalendarApiClient
    participant API as Calendar API
    
    CP->>UC: loadEvents(startDate, endDate)
    UC->>CS: getEvents(startDate, endDate, filters)
    CS->>CR: getEvents(startDate, endDate, filters)
    CR->>CAC: getEvents(startDate, endDate, filters)
    CAC->>API: GET /eventos?inicio=...&fin=...
    API-->>CAC: { eventos: [...] }
    CAC-->>CR: CalendarEvent[]
    CR-->>CS: CalendarEvent[]
    CS-->>UC: CalendarEvent[]
    UC-->>CP: { events, loading, error }
    CP->>CP: renderCalendar(events)
```

### 4.3 Gestión de Estado Global

```mermaid
graph TD
    subgraph "Estado Global"
        A[AuthContext]
        N[NotificationContext]
        T[ThemeContext]
    end
    
    subgraph "Estado de Dominio"
        C[Calendar State]
        M[Messages State]
        P[Payments State]
        R[Reports State]
    end
    
    subgraph "Estado Local"
        F[Form State]
        U[UI State]
        V[View State]
    end
    
    subgraph "Persistencia"
        LS[Local Storage]
        SS[Secure Storage]
        CS[Cache Storage]
    end
    
    %% Relaciones
    A --> LS
    A --> SS
    C --> CS
    M --> CS
    P --> CS
    R --> CS
    
    %% Consumo por componentes
    F --> U
    U --> V
    
    classDef global fill:#ffebee
    classDef domain fill:#e8f5e8
    classDef local fill:#e3f2fd
    classDef storage fill:#f3e5f5
    
    class A,N,T global
    class C,M,P,R domain
    class F,U,V local
    class LS,SS,CS storage
```

## 5. Registro de Decisiones Arquitectónicas (ADR)

### ADR-001: Adopción de Arquitectura Hexagonal con DDD

**Decisores**: Equipo de Arquitectura

**Contexto**: La aplicación debe integrar múltiples sistemas externos del ecosistema Santillana manteniendo bajo acoplamiento y alta testabilidad.

**Opciones Evaluadas**:

1. **Arquitectura por capas tradicional**: Separación UI → Business → Data
2. **Arquitectura hexagonal + DDD**: Dominio central con adaptadores
3. **Arquitectura de microservicios en frontend**: Module federation

**Decisión**: Se adopta **Arquitectura Hexagonal con DDD** por:

- **Separación clara**: Lógica de negocio independiente de detalles técnicos
- **Testabilidad**: Dominio testeable sin dependencias externas
- **Flexibilidad**: Cambios en APIs/storage no afectan dominio
- **Escalabilidad**: Nuevos dominios se añaden independientemente

**Consecuencias**:

- ✅ **Positivas**: Mayor mantenibilidad, testabilidad, flexibilidad
- ❌ **Negativas**: Mayor complejidad inicial, curva de aprendizaje
- 🔄 **Mitigación**: Documentación detallada, capacitación del equipo

### ADR-002: Integración de Pagos mediante WebView/iframe

**Decisores**: Equipo Técnico + Santillana

**Contexto**: El sistema de pagos debe mantener seguridad PCI-DSS y reutilizar lógica existente validada.

**Opciones Evaluadas**:

1. **Integración API nativa completa**: SDK de pagos en React
2. **WebView/iframe embebido**: Reutilización del sistema web existente
3. **Redirección externa**: Salir de la app para pagar

**Decisión**: Se implementa **WebView/iframe embebido** por:

- **Seguridad**: Cumplimiento PCI-DSS sin cambios
- **Tiempo desarrollo**: Reutilización de lógica validada
- **Mantenimiento**: Actualizaciones centralizadas
- **UX**: Experiencia integrada en la app

Consecuencias:

- ✅ **Positivas**: Menor riesgo de seguridad, desarrollo más rápido
- ❌ **Negativas**: UX ligeramente inferior, complejidad de comunicación
- 🔄 **Mitigación**: Bridge seguro app-WebView, manejo de errores robusto

### ADR-003: Autenticación OpenID Connect con PKCE

**Decisores**: Equipo Seguridad + Desarrollo

**Contexto**: La aplicación requiere integración con Santillana Connect manteniendo máxima seguridad.

**Opciones Evaluadas**:

1. **OAuth 2.0 Implicit Flow**: Flujo simplificado
2. **OAuth 2.0 Authorization Code**: Flujo estándar
3. **OpenID Connect + PKCE**: Flujo más seguro para móviles

**Decisión**: Se implementa **OpenID Connect + PKCE** por:

- **Seguridad móvil**: PKCE previene ataques de intercepción
- **Estándar**: Cumple mejores prácticas de OAuth 2.0
- **Integración**: Compatible con infraestructura Santillana
- **Tokens seguros**: JWT con información de usuario

**Consecuencias**:

- ✅ **Positivas**: Máxima seguridad, estándar de industria
- ❌ **Negativas**: Complejidad adicional de implementación
- 🔄 **Mitigación**: Librerías estándar, documentación detallada

### ADR-004: Atomic Design para Componentes UI

**Decisores**: Equipo Frontend

**Contexto**: La aplicación requiere componentes reutilizables y sistema de diseño consistente.

**Opciones Evaluadas**:

1. **Componentes por página**: Un componente por funcionalidad
2. **Atomic Design**: Átomos → Moléculas → Organismos → Templates
3. **Componentes por dominio**: Agrupación por contexto de negocio

**Decisión**: Se adopta **Atomic Design** por:

- **Reutilización**: Componentes composables y reutilizables
- **Consistencia**: Sistema de diseño coherente
- **Mantenibilidad**: Cambios localizados por nivel
- **Testing**: Pruebas granulares por componente

**Consecuencias**:

- ✅ Positivas: Mayor reutilización, consistencia visual
- ❌ Negativas: Estructura más compleja inicialmente

### ADR-005: Caché Híbrida para Soporte Offline

**Decisores**: Equipo Técnico

**Contexto**: La aplicación debe funcionar con conectividad limitada manteniendo datos actualizados.

**Opciones Evaluadas**:

1. Sin soporte offline: Requiere conexión siempre
2. Cache completa: Toda la aplicación funciona offline
3. Cache híbrida: Funcionalidad esencial offline, sincronización automática

**Decisión**: Se implementa **Cache híbrida** por:

- **Balance UX/Complejidad**: Funcionalidad esencial disponible offline
- **Estrategia TTL**: Diferentes políticas según tipo de dato
- **Sincronización**: Actualización automática al recuperar conexión
- **Almacenamiento eficiente**: Cache selectiva según uso

Consecuencias:

- ✅ **Positivas**: Mejor UX con conectividad intermitente
- ❌ **Negativas**: Complejidad de sincronización, posibles conflictos
- 🔄 **Mitigación**: Estrategias de resolución de conflictos, indicadores de estado

### ADR-006: Testing Strategy - Pirámide de Testing

**Decisores**: Equipo QA + Desarrollo

**Contexto**: La aplicación requiere alta calidad con ciclos de desarrollo ágiles.

**Opciones Evaluadas**:

1. **Testing manual predominante**: Mayor tiempo, menor automatización
2. **E2E testing predominante**: Mayor cobertura, menor velocidad
3. **Pirámide de testing**: Mayoría unitarios, selectivos E2E

**Decisión**: Se adopta **Pirámide de testing** por:

- **Velocidad**: Tests unitarios rápidos en CI/CD
- **Cobertura**: 50% unitarios, 20% integración, 10% E2E
- **Feedback rápido**: Detección temprana de problemas
- **Mantenibilidad**: Tests granulares más fáciles de mantener

**Consecuencias**:

- ✅ **Positivas**: CI/CD rápido, alta confianza en código
- ❌ **Negativas**: Inversión inicial en setup de testing
- 🔄 **Mitigación**: Herramientas automatizadas, templates de testing

## 6. Diagramas de Secuencia

### 6.1 Flujo de Autenticación OpenID Connect + PKCE

```mermaid
sequenceDiagram
    participant U as Usuario
    participant A as App Agenda Familiar
    participant AS as Auth Service
    participant SC as Santillana Connect
    participant SCIM as API SCIM
    participant TS as Token Storage
    
    Note over U,TS: Inicio de sesión
    U->>A: Toca "Iniciar Sesión"
    A->>AS: initiateLogin()
    AS->>AS: generatePKCE(codeVerifier, codeChallenge)
    AS->>SC: GET /connect/authorize<br/>?client_id=...&code_challenge=...
    SC->>U: Redirige a formulario login
    U->>SC: Ingresa credenciales
    SC->>SC: Valida credenciales
    SC->>A: Redirige con authorization_code
    
    Note over A,TS: Intercambio de tokens
    A->>AS: handleCallback(authCode)
    AS->>SC: POST /connect/token<br/>{code, code_verifier, client_id}
    SC->>AS: {access_token, id_token, refresh_token}
    AS->>TS: storeTokens(tokens)
    AS->>SCIM: GET /Users/<USER>/>Authorization: Bearer access_token
    SCIM->>AS: {user_profile, roles}
    AS->>A: AuthResult{user, tokens}
    A->>U: Navega a HomePage
    
    Note over U,TS: Gestión de tokens
    loop Cada API call
        A->>AS: getValidToken()
        AS->>TS: getStoredTokens()
        alt Token válido
            TS->>AS: access_token
            AS->>A: access_token
        else Token expirado
            AS->>SC: POST /connect/token<br/>{refresh_token}
            SC->>AS: {new_access_token}
            AS->>TS: updateTokens(new_tokens)
            AS->>A: new_access_token
        end
    end
    
    Note over U,TS: Cierre de sesión
    U->>A: Toca "Cerrar Sesión"
    A->>AS: logout()
    AS->>SC: GET /connect/endsession
    AS->>TS: clearTokens()
    AS->>A: LogoutResult
    A->>U: Navega a LoginPage
```

### 6.2 Flujo de Pago con WebView

```mermaid
sequenceDiagram
    participant U as Usuario
    participant A as App
    participant PW as PaymentWebView
    participant PS as Payment Service
    participant WS as WebView Server
    participant PG as Payment Gateway
    participant SAP as Sistema SAP
    
    Note over U,SAP: Inicialización de pago
    U->>A: Selecciona "Realizar Pago"
    A->>PS: getPaymentProducts(familyId)
    PS->>A: ProductList
    A->>U: Muestra productos disponibles
    U->>A: Selecciona productos
    
    Note over U,SAP: Carga de WebView
    A->>PW: loadPaymentWebView(products, userToken)
    PW->>PW: buildPaymentURL(params)
    PW->>WS: GET /payment-checkout?token=...&products=...
    WS->>PW: HTML + JavaScript del checkout
    PW->>A: postMessage({type: 'ready'})
    
    Note over U,SAP: Proceso de pago
    U->>PW: Ingresa datos de tarjeta
    PW->>WS: Envía datos de pago
    WS->>PG: ProcessPayment(cardData, amount)
    PG->>WS: PaymentResult{success, transactionId}
    
    alt Pago exitoso
        WS->>SAP: RegisterPayment(transactionId, products)
        SAP->>WS: PaymentRegistered{orderId, invoiceId}
        WS->>PW: PaymentSuccess{orderId}
        PW->>A: postMessage({type: 'paymentCompleted', orderId})
        A->>U: Muestra confirmación de pago
        A->>PS: refreshPaymentStatus()
    else Pago fallido
        WS->>PW: PaymentError{errorCode, message}
        PW->>A: postMessage({type: 'paymentFailed', error})
        A->>U: Muestra error de pago
    end
    
    Note over U,SAP: Seguimiento post-pago
    SAP->>SAP: GenerateShippingOrder(orderId)
    SAP->>U: Envía email con comprobante
    A->>PS: trackShipment(orderId)
    PS->>A: ShippingStatus
    A->>U: Actualiza estado de envío
```

### 6.3 Flujo de Sincronización de Calendario

```mermaid
sequenceDiagram
    participant U as Usuario
    participant CP as CalendarPage
    participant UC as useCalendar
    participant CS as CalendarService
    participant CR as CalendarRepo
    participant CAC as CalendarApiClient
    participant CACHE as Cache Service
    participant API as Calendar API
    
    Note over U,API: Carga inicial
    U->>CP: Abre calendario
    CP->>UC: loadEvents(currentMonth)
    UC->>CS: getEvents(startDate, endDate)
    CS->>CR: getEvents(startDate, endDate)
    
    Note over CR,API: Verificación de caché
    CR->>CACHE: getCachedEvents(cacheKey)
    alt Cache válida (< 4 horas)
        CACHE->>CR: CachedEvents
        CR->>CS: CachedEvents
    else Cache expirada o no existe
        CR->>CAC: fetchEvents(startDate, endDate)
        CAC->>API: GET /eventos?inicio=...&fin=...
        API->>CAC: {eventos: [...]}
        CAC->>CR: MappedEvents
        CR->>CACHE: storeEvents(cacheKey, events, ttl)
        CR->>CS: FreshEvents
    end
    
    CS->>UC: EventsWithCategories
    UC->>CP: {events, loading: false}
    CP->>U: Renderiza calendario con eventos
    
    Note over U,API: Actualización pull-to-refresh
    U->>CP: Pull to refresh
    CP->>UC: refresh
    UC->>CS: refreshEvents(currentDateRange)
    CS->>CR: refreshEvents(startDate, endDate, forceRefresh: true)
    CR->>CACHE: invalidateCache(cacheKey)
    CR->>CAC: fetchEvents(startDate, endDate)
    CAC->>API: GET /eventos?inicio=...&fin=...
    API->>CAC: {eventos: [...]}
    CAC->>CR: FreshEvents
    CR->>CACHE: storeEvents(cacheKey, events, ttl)
    CR->>CS: UpdatedEvents
    CS->>UC: UpdatedEventsWithCategories
    UC->>CP: {events, loading: false, refreshing: false}
    CP->>U: Actualiza vista con nuevos eventos
    
    Note over U,API: Filtrado por categorías
    U->>CP: Selecciona filtros de categoría
    CP->>UC: filterEvents(selectedCategories)
    UC->>CS: getFilteredEvents(categories)
    CS->>CS: applyFilters(events, categories)
    CS->>UC: FilteredEvents
    UC->>CP: {events: filteredEvents}
    CP->>U: Muestra eventos filtrados
```

## 7. Referencias Cruzadas

### 7.1 Ampliaciones al Documento "02. Arquitectura del Sistema"

| Sección Original                  | Ampliación en este Documento                        | Tipo de Cambio                                                                      |
|-----------------------------------|-----------------------------------------------------|--------------------------------------------------------------------------------------|
| **Visión General**                | Diagrama C4 - Vista de Containers                   | ✨ **Amplificación:** Detalle técnico de containers                                   |
| **Frontend - App Agenda Familiar**| Estructura de Módulos                               | 🔄 **Refinamiento:** Arquitectura detallada por capas                                |
| **Módulo de Autenticación**       | ADR-003 + Secuencia Auth                            | ✨ **Amplificación:** Decisiones técnicas + flujo detallado                           |
| **Sistema de Pagos**              | ADR-002 + Secuencia Pagos                           | ✨ **Amplificación:** Justificación WebView + implementación                          |
| **Empaquetado y Despliegue**      | Testing Strategy                                    | ✨ **Amplificación:** Estrategia de testing integrada                                 |


### 7.2 Coherencia con Documentos Existentes

| Documento de Referencia                 | Alineación   | Observaciones                                                                      |
|-----------------------------------------|--------------|------------------------------------------------------------------------------------|
| **03. Flujos de Trabajo Principales**   | ✅ **Total** | Flujos implementados en hooks y servicios de dominio                               |
| **05. APIs y Endpoints**                | ✅ **Total** | Contratos implementados en adaptadores de infraestructura                          |
| **06. Aspectos Técnicos Relevantes**    | ✅ **Total** | Stack tecnológico y patrones respetados                                            |
| **07. Decisiones de Diseño Clave**      | 🔄 **Expandido** | ADRs formalizan y amplían decisiones documentadas                                   |
| **13. Estrategia de Testing**           | ✅ **Total** | Arquitectura de testing alineada con estructura de carpetas                        |


### 7.3 Modificaciones Propuestas

**TODO**: Las siguientes secciones requieren validación del equipo de Santillana:

1. **Estructura de tokens JWT**: Validar campos específicos requeridos por SCIM
2. **Políticas de TTL de caché**: Confirmar tiempos según criticidad de datos
3. **Configuración WebView**: Validar parámetros de seguridad y dominio confiable
4. **Endpoints de callback**: Confirmar URLs de redirect para OpenID Connect

## 8. Checklist de Validación
   
### 8.1 Validación Arquitectónica
   
#### Principios DDD y Hexagonal

- [ ] **Dominio independiente**: Lógica de negocio sin dependencias externas
- [ ] **Puertos y adaptadores**: Interfaces claras entre capas
- [ ] **Separación de responsabilidades**: Cada capa tiene una responsabilidad específica
- [ ] **Inversión de dependencias**: Dependencias apuntan hacia el dominio

#### Atomic Design

- [ ] **Jerarquía clara**: Átomos → Moléculas → Organismos → Templates
- [ ] **Componentes reutilizables**: Elementos composables entre diferentes contextos
- [ ] **Sistema de diseño coherente**: Tokens de diseño consistentes
- [ ] **Testing granular**: Pruebas por nivel de componente

### 8.2 Validación de Integración

#### Santillana Connect (OpenID)

- [ ] **Flujo PKCE completo**: code_verifier, code_challenge implementados
- [ ] **Gestión de tokens**: Almacenamiento seguro, refresh automático
- [ ] **Manejo de errores**: Códigos de error documentados manejados
- [ ] **Logout seguro**: Limpieza completa de sesión

#### APIs del Ecosistema

- [ ] **Contratos respetados**: Interfaces según documentación de APIs
- [ ] **Mapeo de datos**: Transformación correcta API ↔ Dominio
- [ ] **Manejo de errores**: Reintentos y fallbacks implementados
- [ ] **Caché implementada**: TTL según criticidad de datos

#### WebView de Pagos

- [ ] **Comunicación segura**: Validación de origen de mensajes
- [ ] **Manejo de estados**: Todos los estados de pago cubiertos
- [ ] **Fallbacks**: Comportamiento ante errores de conectividad
- [ ] **Seguridad**: Validación de parámetros, no exposición de datos sensibles

### 8.3 Validación de Calidad

#### Testing

- [ ] **Cobertura mínima**: 50% global, 85% componentes críticos
- [ ] **Tipos de testing**: Unitario, integración, E2E implementados
- [ ] **Mocks apropiados**: Simulación correcta de dependencias externas
- [ ] **CI/CD integrado**: Tests ejecutados automáticamente

#### Rendimiento

- [ ] **Lazy loading**: Módulos cargados bajo demanda
- [ ] **Virtual scrolling**: Para listas largas
- [ ] **Optimización de imágenes**: Carga diferida, compresión
- [ ] **Bundle size**: Análisis y optimización del tamaño final

#### Seguridad

- [ ] **Almacenamiento seguro**: Tokens en secure storage
- [ ] **Validación de entrada**: Sanitización de datos de usuario
- [ ] **Comunicación segura**: HTTPS, validación de certificados
- [ ] **Principio de menor privilegio**: Permisos mínimos necesarios

### 8.4 Validación de UX/UI

#### Accesibilidad

- [ ] **WCAG 2.1 AA**: Cumplimiento de estándares de accesibilidad
- [ ] **Soporte screen readers**: Elementos semánticamente correctos
- [ ] **Contraste**: Ratios de color apropiados
- [ ] **Navegación por teclado**: Orden de tabulación lógico

#### Responsive Design

- [ ] **Múltiples resoluciones**: Adaptación iOS/Android
- [ ] **Orientación**: Soporte portrait/landscape
- [ ] **Dispositivos objetivo**: Funcionalidad en dispositivos de gama media/baja
- [ ] **Performance**: 60fps en animaciones, carga < 3s

### 8.5 Validación de Mantenibilidad

#### Documentación

- [ ] **Código autodocumentado**: Nombres claros, funciones pequeñas
- [ ] **README actualizado**: Instrucciones de setup y desarrollo
- [ ] **Comentarios apropiados**: Explicación de lógica compleja
- [ ] **Changelog**: Registro de cambios significativos

#### Monitorización

- [ ] **Logging estructurado**: Logs útiles para debugging
- [ ] **Métricas de uso**: Analítica de funcionalidades clave
- [ ] **Error tracking**: Captura y reporte de errores en producción
- [ ] **Performance monitoring**: Seguimiento de métricas clave

## 9. Glosario

| Término           | Definición                                                                                                              | Contexto de Uso          |
|-------------------|-------------------------------------------------------------------------------------------------------------------------|--------------------------|
| **Adapter**       | Implementación concreta de un puerto/interface, traduce entre dominio e infraestructura                                 | Patrón Hexagonal         |
| **Aggregate**     | Grupo de entidades relacionadas que se tratan como una unidad en DDD                                                    | Domain-Driven Design     |
| **Atom**          | Componente UI más básico e indivisible en Atomic Design                                                                 | Sistema de Componentes   |
| **Bounded Context** | Límite explícito dentro del cual un modelo de dominio se aplica                                                        | Domain-Driven Design     |
| **Domain Service** | Servicio que encapsula lógica de negocio que no pertenece naturalmente a una entidad                                   | Capa de Dominio          |
| **Entity**        | Objeto del dominio que tiene identidad única y ciclo de vida                                                            | Domain-Driven Design     |
| **Hook**          | Función de React que permite usar estado y efectos en componentes funcionales                                           | React                    |
| **Molecule**      | Componente UI compuesto por varios átomos                                                                               | Atomic Design            |
| **Organism**      | Componente UI complejo compuesto por moléculas y/o átomos                                                               | Atomic Design            |
| **PKCE**          | Proof Key for Code Exchange – extensión de OAuth 2.0 para mayor seguridad                                               | Autenticación            |
| **Port**          | Interface que define cómo el dominio interactúa con el mundo exterior                                                   | Patrón Hexagonal         |
| **Repository**    | Patrón que encapsula la lógica de acceso a datos                                                                        | Domain-Driven Design     |
| **Template**      | Estructura de página que organiza organismos en layouts                                                                 | Atomic Design            |
| **TTL**           | Time To Live – tiempo de vida de datos en caché                                                                          | Gestión de Caché         |
| **Value Object**  | Objeto del dominio que se define por sus valores, no por identidad                                                      | Domain-Driven Design     |

## 10. Apéndices

### 10.1 Herramientas y Tecnologías Detalladas

#### Stack Principal

```json
{
  "runtime": {
    "ionic": "8.5.0",
    "react": "19.0.0",
    "typescript": "5.1.6",
    "capacitor": "7.2.0"
  },
  "build": {
    "vite": "5.2.0",
    "rollup": "4.9.0"
  },
  "testing": {
    "vitest": "0.34.6",
    "cypress": "13.5.0",
    "@testing-library/react": "14.0.0"
  },
  "development": {
    "eslint": "9.20.1",
    "prettier": "3.2.5"
  }
}
```

#### Librerías Específicas

- **Autenticación**: `@openid/appauth` - Cliente OpenID Connect
- **Storage**: `@ionic/storage` - Almacenamiento local
- **HTTP**: `axios` - Cliente HTTP con interceptores
- **Gráficos**: `recharts` - Visualización de datos académicos
- **Forms**: `react-hook-form` - Gestión de formularios
- **Testing**: `msw` - Mock Service Worker para tests

### 10.2 Patrones de Naming Conventions

#### Archivos y Carpetas

- **Componentes**: PascalCase (`CalendarPage.tsx`)
- **Hooks**: camelCase con prefijo use (`useCalendar.ts`)
- **Servicios**: PascalCase con sufijo Service (`CalendarService.ts`)
- **Tipos**: PascalCase (`CalendarEvent.ts`)
- **Constantes**: SCREAMING_SNAKE_CASE (`API_ENDPOINTS.ts`)

#### Variables y Funciones

- **Variables**: camelCase (`calendarEvents`)
- **Funciones**: camelCase (`getCalendarEvents`)
- **Componentes**: PascalCase (`CalendarPage`)
- **Interfaces**: PascalCase con prefijo I (`ICalendarEvent`)
- **Enums**: PascalCase (`EventCategory`)