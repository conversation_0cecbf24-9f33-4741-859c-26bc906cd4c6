# State Management Fix for OIDC Authentication

## Problem Identified

The "No matching state found in storage" error was caused by conflicting state management systems:

1. **CapacitorAuthService** was manually generating and storing state in `localStorage` with key `capacitor_oidc_state`
2. **UserManager** was expecting to find state in `sessionStorage` with prefix `oidc.santillana.state.`

When `userManager.signinRedirectCallback()` was called, it couldn't find the state because it was looking in the wrong storage location with the wrong key format.

## Solution Implemented

### 1. **Unified State Management**
- Removed manual state generation in CapacitorAuthService
- Now using UserManager's built-in state management system
- CapacitorAuthService captures the authorization URL from UserManager instead of manually constructing it

### 2. **Storage Configuration Update**
- Updated UserManager to use `localStorage` for state storage on native platforms
- This ensures state persists during the browser switch that happens in native authentication flow
- Web platforms continue to use `sessionStorage` to avoid persistence issues

### 3. **Authorization URL Capture**
- Implemented a clever technique to capture the authorization URL that UserManager generates
- Temporarily overrides `window.location.assign` and `window.location.replace` to intercept the redirect
- This ensures proper PKCE parameters and state management while allowing external browser opening

## Key Changes Made

### CapacitorAuthService.ts
```typescript
// OLD: Manual state generation
const state = this.generateState();
localStorage.setItem('capacitor_oidc_state', state);

// NEW: Use UserManager's state management
const authUrl = await this.createAuthorizationUrl();
```

### user-manager.config.ts
```typescript
// OLD: Always use sessionStorage
stateStore: new WebStorageStateStore({
  store: window.sessionStorage,
  prefix: "oidc.santillana.state."
}),

// NEW: Use localStorage for native, sessionStorage for web
stateStore: new WebStorageStateStore({
  store: Capacitor.isNativePlatform() ? window.localStorage : window.sessionStorage,
  prefix: "oidc.santillana.state."
}),
```

## How the Fix Works

### Authorization Flow (Native)
1. User clicks login button
2. CapacitorAuthService calls `createAuthorizationUrl()`
3. This method temporarily overrides window location methods
4. Calls `userManager.signinRedirect()` which generates proper state and PKCE parameters
5. Instead of redirecting, captures the authorization URL
6. Opens the captured URL in external browser
7. State is properly stored in localStorage with UserManager's expected format

### Callback Flow (Native)
1. External browser redirects to `capacitor://localhost/callback?code=...&state=...`
2. App receives deep link and calls `handleDeepLink()`
3. Calls `userManager.signinRedirectCallback(callbackUrl)`
4. UserManager finds the state in localStorage (where it stored it)
5. State verification passes, tokens are exchanged successfully

## Testing the Fix

### Before Testing
Ensure you have the latest changes:
```bash
# Clear any existing state
# In browser console or app:
localStorage.clear();
sessionStorage.clear();
```

### Test Steps
1. **Android Emulator Test:**
   ```bash
   npx cap build android
   npx cap run android
   ```
   
2. **Login Flow:**
   - Open app in Android emulator
   - Navigate to login page
   - Click "Iniciar Sesión con Santillana Connect"
   - Enter valid credentials
   - Verify successful authentication without state errors

3. **Debug Logging:**
   Enable debug logging to monitor the flow:
   ```
   🔐 [AUTH] CapacitorAuthService - Creating signin request with UserManager
   🔐 [AUTH] CapacitorAuthService - Authorization URL created
   🔐 [AUTH] CapacitorAuthService - Processing callback with UserManager
   🔐 [AUTH] CapacitorAuthService - Authentication successful
   ```

### Expected Results
- ✅ No "No matching state found in storage" errors
- ✅ Successful authentication flow completion
- ✅ Proper token storage and user profile retrieval
- ✅ Smooth transition from external browser back to app

## Debugging State Issues

If you still encounter state issues, check:

1. **State Storage Location:**
   ```javascript
   // Check what's stored
   console.log('localStorage state keys:', 
     Object.keys(localStorage).filter(k => k.includes('state')));
   console.log('sessionStorage state keys:', 
     Object.keys(sessionStorage).filter(k => k.includes('state')));
   ```

2. **UserManager Configuration:**
   ```javascript
   // Check UserManager settings
   userManager.settings.then(settings => {
     console.log('UserManager settings:', settings);
   });
   ```

3. **Platform Detection:**
   ```javascript
   // Verify platform detection
   console.log('Is native platform:', Capacitor.isNativePlatform());
   console.log('Platform:', Capacitor.getPlatform());
   ```

## Additional Improvements

The fix also includes:

1. **Better Error Handling:** Enhanced error messages for state-related issues
2. **Cleanup Logic:** Proper cleanup of authentication state on completion or error
3. **Timeout Protection:** 5-second timeout for authorization URL capture
4. **Fallback Mechanisms:** Graceful handling of edge cases

## Verification Commands

```bash
# Test the authentication flow
npm run dev  # For web testing
npx cap run android  # For Android testing

# Check for errors in console
# Look for successful authentication logs
# Verify no state mismatch errors
```

This fix ensures that the OIDC authentication flow works seamlessly across web and native platforms by properly managing the state parameter throughout the entire authentication lifecycle.
