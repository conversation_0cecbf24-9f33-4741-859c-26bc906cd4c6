import { User } from "oidc-client-ts";
import { Preferences } from "@capacitor/preferences";
import { Capacitor } from "@capacitor/core";

/**
 * Service for managing authentication tokens and user data
 * Handles both web localStorage and native Capacitor Preferences
 */
export class AuthStorageService {
  private static readonly TOKEN_KEYS = {
    ACCESS_TOKEN: "oidc.access_token",
    ID_TOKEN: "oidc.id_token", 
    REFRESH_TOKEN: "oidc.refresh_token",
    USER_DATA: "oidc.user_data",
    EXPIRES_AT: "oidc.expires_at"
  } as const;

  private static isNative = Capacitor.getPlatform() !== "web";

  /**
   * Store authentication tokens securely
   */
  static async storeTokens(user: User): Promise<void> {
    try {
      const tokenData = {
        access_token: user.access_token,
        id_token: user.id_token,
        refresh_token: user.refresh_token,
        expires_at: user.expires_at,
        user_data: JSON.stringify(user.profile)
      };

      if (this.isNative) {
        // Use Capacitor Preferences for native apps
        await Promise.all([
          Preferences.set({ key: this.TOKEN_KEYS.ACCESS_TOKEN, value: tokenData.access_token || "" }),
          Preferences.set({ key: this.TOKEN_KEYS.ID_TOKEN, value: tokenData.id_token || "" }),
          Preferences.set({ key: this.TOKEN_KEYS.REFRESH_TOKEN, value: tokenData.refresh_token || "" }),
          Preferences.set({ key: this.TOKEN_KEYS.EXPIRES_AT, value: tokenData.expires_at?.toString() || "" }),
          Preferences.set({ key: this.TOKEN_KEYS.USER_DATA, value: tokenData.user_data })
        ]);
      }

      console.log("✅ Tokens stored successfully", {
        hasAccessToken: !!tokenData.access_token,
        hasIdToken: !!tokenData.id_token,
        hasRefreshToken: !!tokenData.refresh_token,
        expiresAt: tokenData.expires_at
      });
    } catch (error) {
      console.error("❌ Error storing tokens:", error);
      throw new Error("Failed to store authentication tokens");
    }
  }

  /**
   * Retrieve stored tokens
   */
  static async getStoredTokens(): Promise<{
    access_token?: string;
    id_token?: string;
    refresh_token?: string;
    expires_at?: number;
    user_data?: any;
  }> {
    try {
      if (this.isNative) {
        const [accessToken, idToken, refreshToken, expiresAt, userData] = await Promise.all([
          Preferences.get({ key: this.TOKEN_KEYS.ACCESS_TOKEN }),
          Preferences.get({ key: this.TOKEN_KEYS.ID_TOKEN }),
          Preferences.get({ key: this.TOKEN_KEYS.REFRESH_TOKEN }),
          Preferences.get({ key: this.TOKEN_KEYS.EXPIRES_AT }),
          Preferences.get({ key: this.TOKEN_KEYS.USER_DATA })
        ]);

        return {
          access_token: accessToken.value || undefined,
          id_token: idToken.value || undefined,
          refresh_token: refreshToken.value || undefined,
          expires_at: expiresAt.value ? parseInt(expiresAt.value, 10) : undefined,
          user_data: userData.value ? JSON.parse(userData.value) : undefined
        };
      }

      // For web, tokens are managed by oidc-client-ts in localStorage
      return {};
    } catch (error) {
      console.error("❌ Error retrieving tokens:", error);
      return {};
    }
  }

  /**
   * Clear all stored authentication data
   */
  static async clearTokens(): Promise<void> {
    try {
      if (this.isNative) {
        await Promise.all([
          Preferences.remove({ key: this.TOKEN_KEYS.ACCESS_TOKEN }),
          Preferences.remove({ key: this.TOKEN_KEYS.ID_TOKEN }),
          Preferences.remove({ key: this.TOKEN_KEYS.REFRESH_TOKEN }),
          Preferences.remove({ key: this.TOKEN_KEYS.EXPIRES_AT }),
          Preferences.remove({ key: this.TOKEN_KEYS.USER_DATA })
        ]);
      }

      console.log("✅ Tokens cleared successfully");
    } catch (error) {
      console.error("❌ Error clearing tokens:", error);
      throw new Error("Failed to clear authentication tokens");
    }
  }

  /**
   * Check if tokens are expired
   */
  static async isTokenExpired(): Promise<boolean> {
    try {
      const tokens = await this.getStoredTokens();
      if (!tokens.expires_at) return true;
      
      const now = Math.floor(Date.now() / 1000);
      const isExpired = now >= tokens.expires_at;
      
      return isExpired;
    } catch (error) {
      console.error("❌ Error checking token expiration:", error);
      return true;
    }
  }
}
