/**
 * Modern Capacitor OIDC authentication service
 * Implements secure WebView-based authentication flow for iOS and Android
 * Uses Capacitor InAppBrowser with proper deep link handling
 */

import { Capacitor, CapacitorCookies } from '@capacitor/core';
import { InAppBrowser, DefaultSystemBrowserOptions } from '@capacitor/inappbrowser';
import { App } from '@capacitor/app';
import { Preferences } from '@capacitor/preferences';
import { environmentConfig, debugLog } from '../config/environment.config';
import { CryptoFallback } from '../utils/crypto-fallback';
import { NativeHttp } from '../utils/native-http';

/**
 * Enhanced logging for Chrome DevTools remote debugging
 * Ensures all logs are properly forwarded when debugging Android via chrome://inspect
 */
class AuthLogger {
  private static isRemoteDebugging = Capacitor.isNativePlatform();

  static log(message: string, data?: any) {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
    const logMessage = `[${timestamp}] ${message}`;

    // Always use console.log for remote debugging compatibility
    if (data !== undefined) {
      console.log(logMessage, data);
    } else {
      console.log(logMessage);
    }

    // Also use debugLog for consistency with existing system
    if (data !== undefined) {
      debugLog(`${message}`, data);
    } else {
      debugLog(message);
    }
  }

  static error(message: string, error?: any) {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
    const logMessage = `[${timestamp}] ❌ ${message}`;

    console.error(logMessage, error || '');
    debugLog(`❌ ${message}`, error);
  }

  static warn(message: string, data?: any) {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
    const logMessage = `[${timestamp}] ⚠️ ${message}`;

    console.warn(logMessage, data || '');
    debugLog(`⚠️ ${message}`, data);
  }

  static info(message: string, data?: any) {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
    const logMessage = `[${timestamp}] ℹ️ ${message}`;

    console.info(logMessage, data || '');
    debugLog(`ℹ️ ${message}`, data);
  }
}

interface AuthState {
  state: string;
  codeVerifier: string;
  nonce: string;
  redirectUri: string;
  timestamp: number;
}

interface AuthResult {
  accessToken: string;
  idToken: string;
  refreshToken?: string;
  expiresIn: number;
  profile: {
    sub: string;
    name?: string;
    email?: string;
    [key: string]: any;
  };
}

export class CapacitorAuthService {
  private static authInProgress = false;
  private static authPromiseResolve: ((value: AuthResult) => void) | null = null;
  private static authPromiseReject: ((reason?: any) => void) | null = null;
  private static deepLinkListener: any = null;

  /**
   * Initialize modern WebView authentication service
   */
  static async initialize(): Promise<void> {
    if (!Capacitor.isNativePlatform()) {
      AuthLogger.info('Not native platform, skipping initialization');
      return;
    }

    AuthLogger.log('🔐 [AUTH] Initializing modern WebView authentication service');
    AuthLogger.log('🔐 [AUTH] Platform:', Capacitor.getPlatform());
    AuthLogger.log('🔐 [AUTH] InAppBrowser available:', Capacitor.isPluginAvailable('InAppBrowser'));
    AuthLogger.log('🔐 [AUTH] Native HTTP available:', NativeHttp.isNativeHttpAvailable());
    AuthLogger.log('🔐 [AUTH] Web Crypto available:', CryptoFallback.isWebCryptoAvailable());

    // Test crypto functionality during initialization
    try {
      await CryptoFallback.testCrypto();
      AuthLogger.log('🔐 [AUTH] Crypto functionality test passed');
    } catch (error) {
      AuthLogger.error('Crypto functionality test failed:', error);
      throw new Error(`Crypto initialization failed: ${error}`);
    }

    // Test native HTTP functionality during initialization
    try {
      await NativeHttp.testHttp();
      AuthLogger.log('🔐 [AUTH] Native HTTP functionality test passed');
    } catch (error) {
      AuthLogger.warn('Native HTTP functionality test failed (will use fetch fallback):', error);
      // Don't throw error here as fetch fallback should work for web
    }

    // Set up deep link listener for authentication callbacks (with await to fix warning)
    this.deepLinkListener = await App.addListener('appUrlOpen', (event) => {
      AuthLogger.log('🔗 [AUTH] Deep link received:', event.url);
      this.handleAuthCallback(event.url);
    });

    // Listen for app state changes (with await to fix warning)
    await App.addListener('appStateChange', ({ isActive }) => {
      if (isActive && this.authInProgress) {
        AuthLogger.log('📱 [AUTH] App became active during authentication');
      }
    });

    AuthLogger.log('✅ [AUTH] Authentication service initialization complete');
  }

  /**
   * Check for existing authentication without forcing fresh login
   * Use this method to restore existing sessions on app startup
   */
  static async silentSignIn(): Promise<AuthResult | null> {
    if (!Capacitor.isNativePlatform()) {
      return null;
    }

    try {
      AuthLogger.log('🔍 [AUTH] Checking for existing authentication session');
      const existingUser = await this.getCurrentUser();

      if (existingUser) {
        AuthLogger.log('👤 [AUTH] Found existing valid session', {
          userId: existingUser.profile.sub,
          userName: existingUser.profile.name,
          userEmail: existingUser.profile.email
        });
        return existingUser;
      }

      AuthLogger.log('❌ [AUTH] No existing valid session found');
      return null;
    } catch (error) {
      AuthLogger.error('Error during silent sign in:', error);
      return null;
    }
  }

  /**
   * Start OIDC authentication flow using secure WebView
   * This method always forces fresh authentication by clearing browser session
   */
  static async signIn(): Promise<AuthResult> {
    if (!Capacitor.isNativePlatform()) {
      throw new Error('CapacitorAuthService is only for native platforms');
    }

    if (this.authInProgress) {
      AuthLogger.warn('Authentication already in progress');
      throw new Error('Authentication already in progress');
    }

    try {
      this.authInProgress = true;
      AuthLogger.log('🚀 [AUTH] Starting secure WebView authentication');

      // Clear browser session to ensure fresh authentication
      AuthLogger.log('🧹 [AUTH] Clearing browser session for fresh authentication');
      await this.clearBrowserSession();

      // Note: We intentionally skip checking for existing users here to force fresh authentication
      // This ensures that each signIn() call requires the user to enter credentials again
      AuthLogger.log('🔄 [AUTH] Forcing fresh authentication - skipping existing session check');

      // Generate PKCE parameters and state
      AuthLogger.log('🔑 [AUTH] Generating authentication state and PKCE parameters');
      const authState = await this.generateAuthState();

      // Store auth state securely
      AuthLogger.log('💾 [AUTH] Storing authentication state securely');
      await this.storeAuthState(authState);

      // Build authorization URL
      AuthLogger.log('🔗 [AUTH] Building authorization URL');
      const authUrl = await this.buildAuthorizationUrl(authState);

      AuthLogger.log('🌐 [AUTH] Opening secure WebView', {
        urlPreview: authUrl.substring(0, 100) + '...',
        state: authState.state,
        redirectUri: authState.redirectUri
      });

      // Open authentication in secure WebView using default system browser options
      // This ensures compatibility across iOS and Android platforms
      await InAppBrowser.openInSystemBrowser({
        url: authUrl,
        options: DefaultSystemBrowserOptions
      });

      AuthLogger.log('✅ [AUTH] WebView opened successfully, waiting for callback');

      // Return promise that resolves when authentication completes
      return new Promise<AuthResult>((resolve, reject) => {
        this.authPromiseResolve = resolve;
        this.authPromiseReject = reject;

        // Set authentication timeout
        setTimeout(() => {
          if (this.authInProgress) {
            AuthLogger.error('Authentication timeout after 5 minutes');
            this.cleanup();
            reject(new Error('Authentication timeout - please try again'));
          }
        }, 300000); // 5 minutes timeout
      });

    } catch (error) {
      AuthLogger.error('Error starting authentication:', error);
      this.cleanup();
      throw error;
    }
  }

  /**
   * Handle authentication callback from deep link
   */
  private static async handleAuthCallback(url: string): Promise<void> {
    AuthLogger.log('🔄 [AUTH] Processing authentication callback', { url });

    if (!this.authInProgress) {
      AuthLogger.warn('No authentication in progress, ignoring callback');
      return;
    }

    try {
      // Check if this is an OIDC callback URL
      if (!url.includes('/callback') && !url.includes('code=') && !url.includes('error=')) {
        AuthLogger.info('URL is not an OIDC callback, ignoring');
        return;
      }

      AuthLogger.log('🔒 [AUTH] Valid OIDC callback detected, processing...');

      // Close the WebView
      try {
        AuthLogger.log('🚪 [AUTH] Closing WebView');
        await InAppBrowser.close();
      } catch (closeError) {
        AuthLogger.info('WebView already closed or error closing:', closeError);
      }

      // Parse callback URL
      AuthLogger.log('🔍 [AUTH] Parsing callback URL parameters');
      const parsedUrl = new URL(url);
      const code = parsedUrl.searchParams.get('code');
      const state = parsedUrl.searchParams.get('state');
      const error = parsedUrl.searchParams.get('error');
      const errorDescription = parsedUrl.searchParams.get('error_description');

      AuthLogger.log('🔍 [AUTH] Callback parameters:', {
        hasCode: !!code,
        hasState: !!state,
        hasError: !!error,
        codePreview: code ? code.substring(0, 20) + '...' : 'none',
        state: state || 'none'
      });

      // Handle OAuth errors
      if (error) {
        AuthLogger.error('OAuth error received:', { error, errorDescription });
        throw new Error(`Authentication failed: ${error}${errorDescription ? ` - ${errorDescription}` : ''}`);
      }

      // Validate authorization code
      if (!code) {
        AuthLogger.error('No authorization code received');
        throw new Error('No authorization code received from authentication provider');
      }

      if (!state) {
        AuthLogger.error('No state parameter received');
        throw new Error('No state parameter received from authentication provider');
      }

      AuthLogger.log('🎫 [AUTH] Authorization code received, exchanging for tokens');

      // Process the authentication callback
      const authResult = await this.processAuthCallback(code, state);

      AuthLogger.log('🎉 [AUTH] Authentication completed successfully', {
        userId: authResult.profile.sub,
        userName: authResult.profile.name,
        userEmail: authResult.profile.email,
        hasAccessToken: !!authResult.accessToken,
        hasRefreshToken: !!authResult.refreshToken,
        expiresIn: authResult.expiresIn
      });

      // Store tokens securely
      AuthLogger.log('💾 [AUTH] Storing authentication result securely');
      await this.storeAuthResult(authResult);

      // Resolve the authentication promise
      AuthLogger.log('✅ [AUTH] Resolving authentication promise');
      if (this.authPromiseResolve) {
        this.authPromiseResolve(authResult);
      } else {
        AuthLogger.warn('No promise resolver available');
      }

      this.cleanup();

    } catch (error) {
      AuthLogger.error('Error processing auth callback:', error);

      if (this.authPromiseReject) {
        this.authPromiseReject(error);
      }

      this.cleanup();

      // Ensure WebView is closed on error
      try {
        await InAppBrowser.close();
      } catch (closeError) {
        AuthLogger.info('Error closing WebView on error:', closeError);
      }
    }
  }

  /**
   * Generate authentication state with PKCE parameters
   */
  private static async generateAuthState(): Promise<AuthState> {
    try {
      AuthLogger.log('🔑 [AUTH] Generating authentication state with PKCE parameters');

      const state = this.generateRandomString(32);
      const codeVerifier = this.generateRandomString(128);
      const nonce = this.generateRandomString(32);

      AuthLogger.log('🔑 [AUTH] Generated PKCE parameters', {
        stateLength: state.length,
        codeVerifierLength: codeVerifier.length,
        nonceLength: nonce.length,
        statePreview: state.substring(0, 8) + '...',
        codeVerifierPreview: codeVerifier.substring(0, 8) + '...',
        noncePreview: nonce.substring(0, 8) + '...'
      });

      return {
        state,
        codeVerifier,
        nonce,
        redirectUri: environmentConfig.redirectUris?.callback || `${environmentConfig.nativeBaseUrl}/callback`,
        timestamp: Date.now()
      };
    } catch (error) {
      AuthLogger.error('Failed to generate authentication state:', error);
      throw new Error(`Authentication state generation failed: ${error}`);
    }
  }

  /**
   * Store authentication state securely
   */
  private static async storeAuthState(authState: AuthState): Promise<void> {
    try {
      await Preferences.set({
        key: `auth_state_${authState.state}`,
        value: JSON.stringify(authState)
      });

      debugLog('CapacitorAuthService - Auth state stored securely');
    } catch (error) {
      debugLog('CapacitorAuthService - Error storing auth state:', error);
      throw new Error('Failed to store authentication state');
    }
  }

  /**
   * Build authorization URL with PKCE parameters
   */
  private static async buildAuthorizationUrl(authState: AuthState): Promise<string> {
    const authUrl = new URL(`${environmentConfig.authority}/connect/authorize`);

    // Add required OAuth2/OIDC parameters
    authUrl.searchParams.set('client_id', environmentConfig.clientId);
    authUrl.searchParams.set('redirect_uri', authState.redirectUri);
    authUrl.searchParams.set('response_type', 'code');
    authUrl.searchParams.set('scope', environmentConfig.scope);
    authUrl.searchParams.set('state', authState.state);
    authUrl.searchParams.set('nonce', authState.nonce);

    // Add PKCE parameters
    const codeChallenge = await this.generateCodeChallenge(authState.codeVerifier);
    authUrl.searchParams.set('code_challenge', codeChallenge);
    authUrl.searchParams.set('code_challenge_method', 'S256');

    // Add response mode for better compatibility
    authUrl.searchParams.set('response_mode', 'query');

    return authUrl.toString();
  }

  /**
   * Process authentication callback and exchange code for tokens
   */
  private static async processAuthCallback(code: string, state: string): Promise<AuthResult> {
    try {
      // Retrieve stored auth state
      const authState = await this.retrieveAuthState(state);

      if (!authState) {
        throw new Error('Authentication state not found - possible timeout or tampering');
      }

      // Verify state matches
      if (authState.state !== state) {
        throw new Error('State mismatch - possible CSRF attack');
      }

      // Check for timeout (5 minutes)
      if (Date.now() - authState.timestamp > 300000) {
        throw new Error('Authentication state expired - please try again');
      }

      debugLog('CapacitorAuthService - State verification passed, exchanging code for tokens');

      // Exchange authorization code for tokens
      const tokenResponse = await this.exchangeCodeForTokens(code, authState);

      // Parse ID token to get user profile
      const profile = this.parseJwtPayload(tokenResponse.id_token);

      // Create auth result
      const authResult: AuthResult = {
        accessToken: tokenResponse.access_token,
        idToken: tokenResponse.id_token,
        refreshToken: tokenResponse.refresh_token,
        expiresIn: tokenResponse.expires_in || 3600,
        profile: {
          sub: profile.sub,
          name: profile.name,
          email: profile.email,
          ...profile
        }
      };

      // Clean up stored state
      await this.cleanupAuthState(state);

      return authResult;
    } catch (error) {
      // Clean up on error
      await this.cleanupAuthState(state);
      throw error;
    }
  }

  /**
   * Retrieve stored authentication state
   */
  private static async retrieveAuthState(state: string): Promise<AuthState | null> {
    try {
      const result = await Preferences.get({ key: `auth_state_${state}` });

      if (!result.value) {
        return null;
      }

      return JSON.parse(result.value) as AuthState;
    } catch (error) {
      debugLog('CapacitorAuthService - Error retrieving auth state:', error);
      return null;
    }
  }

  /**
   * Exchange authorization code for tokens using native HTTP
   */
  private static async exchangeCodeForTokens(code: string, authState: AuthState): Promise<any> {
    try {
      const tokenEndpoint = `${environmentConfig.authority}/connect/token`;

      AuthLogger.log('🔄 [AUTH] Preparing token exchange request', {
        endpoint: tokenEndpoint,
        grantType: 'authorization_code',
        clientId: environmentConfig.clientId,
        redirectUri: authState.redirectUri,
        hasCodeVerifier: !!authState.codeVerifier,
        usingNativeHttp: NativeHttp.isNativeHttpAvailable()
      });

      // Prepare token request data
      const tokenRequestData = {
        grant_type: 'authorization_code',
        client_id: environmentConfig.clientId,
        code: code,
        redirect_uri: authState.redirectUri,
        code_verifier: authState.codeVerifier
      };

      AuthLogger.log('🌐 [AUTH] Sending token exchange request via native HTTP');

      // Send token request using native HTTP (bypasses CORS)
      const response = await NativeHttp.postForm(tokenEndpoint, tokenRequestData);

      AuthLogger.log('📡 [AUTH] Token exchange response received', {
        status: response.status,
        statusText: response.statusText,
        isSuccess: NativeHttp.isSuccessResponse(response),
        hasData: !!response.data
      });

      if (!NativeHttp.isSuccessResponse(response)) {
        const errorMessage = NativeHttp.getErrorMessage(response);
        AuthLogger.error('Token exchange failed', {
          status: response.status,
          statusText: response.statusText,
          errorMessage,
          responseData: response.data
        });
        throw new Error(`Token exchange failed: ${response.status} ${response.statusText} - ${errorMessage}`);
      }

      const tokenResponse = response.data;

      AuthLogger.log('🎉 [AUTH] Token exchange successful', {
        hasAccessToken: !!tokenResponse.access_token,
        hasIdToken: !!tokenResponse.id_token,
        hasRefreshToken: !!tokenResponse.refresh_token,
        expiresIn: tokenResponse.expires_in,
        tokenType: tokenResponse.token_type,
        scope: tokenResponse.scope
      });

      return tokenResponse;
    } catch (error) {
      AuthLogger.error('Error exchanging code for tokens:', error);

      // Provide more specific error information
      if (error.message && error.message.includes('CORS')) {
        throw new Error('CORS error during token exchange - this should not happen with native HTTP. Please check configuration.');
      } else if (error.message && error.message.includes('Failed to fetch')) {
        throw new Error('Network error during token exchange. Please check internet connection and server availability.');
      }

      throw error;
    }
  }

  /**
   * Store authentication result securely
   */
  private static async storeAuthResult(authResult: AuthResult): Promise<void> {
    try {
      await Preferences.set({
        key: 'auth_tokens',
        value: JSON.stringify({
          accessToken: authResult.accessToken,
          idToken: authResult.idToken,
          refreshToken: authResult.refreshToken,
          expiresAt: Date.now() + (authResult.expiresIn * 1000),
          profile: authResult.profile
        })
      });

      debugLog('CapacitorAuthService - Auth result stored securely');
    } catch (error) {
      debugLog('CapacitorAuthService - Error storing auth result:', error);
      throw new Error('Failed to store authentication result');
    }
  }

  /**
   * Parse JWT payload
   */
  private static parseJwtPayload(token: string): any {
    try {
      const parts = token.split('.');
      if (parts.length !== 3) {
        throw new Error('Invalid JWT format');
      }

      const payload = parts[1];
      const decoded = atob(payload.replace(/-/g, '+').replace(/_/g, '/'));
      return JSON.parse(decoded);
    } catch (error) {
      debugLog('CapacitorAuthService - Error parsing JWT payload:', error);
      throw new Error('Failed to parse ID token');
    }
  }

  /**
   * Generate PKCE code challenge from code verifier using SHA256
   * Uses crypto fallback for compatibility with all WebView environments
   */
  private static async generateCodeChallenge(codeVerifier: string): Promise<string> {
    try {
      AuthLogger.log('🔐 [AUTH] Generating PKCE code challenge with fallback support');
      return await CryptoFallback.generateCodeChallenge(codeVerifier);
    } catch (error) {
      AuthLogger.error('Failed to generate PKCE code challenge:', error);
      throw new Error(`PKCE code challenge generation failed: ${error}`);
    }
  }

  /**
   * Generate a random string for PKCE code verifier, state, and nonce
   * Uses crypto fallback for compatibility with all WebView environments
   */
  private static generateRandomString(length: number): string {
    try {
      AuthLogger.log('🔐 [AUTH] Generating random string with fallback support', { length });
      return CryptoFallback.generateRandomString(length);
    } catch (error) {
      AuthLogger.error('Failed to generate random string:', error);
      throw new Error(`Random string generation failed: ${error}`);
    }
  }

  /**
   * Clean up stored authentication state
   */
  private static async cleanupAuthState(state: string): Promise<void> {
    try {
      await Preferences.remove({ key: `auth_state_${state}` });
      debugLog('CapacitorAuthService - Auth state cleaned up');
    } catch (error) {
      debugLog('CapacitorAuthService - Error cleaning up auth state:', error);
    }
  }

  /**
   * Clear browser session data to ensure fresh authentication
   * This prevents cached credentials from being reused
   */
  private static async clearBrowserSession(): Promise<void> {
    try {
      AuthLogger.log('🧹 [AUTH] Clearing browser session data for fresh authentication');

      // Clear all cookies to remove any cached authentication state
      if (Capacitor.isPluginAvailable('CapacitorCookies')) {
        AuthLogger.log('🍪 [AUTH] Clearing all browser cookies');
        await CapacitorCookies.clearAllCookies();

        // Also clear cookies specifically for the OAuth2 provider domain
        const oauthDomain = new URL(environmentConfig.authority).hostname;
        AuthLogger.log('🍪 [AUTH] Clearing cookies for OAuth2 provider domain:', oauthDomain);

        try {
          await CapacitorCookies.clearCookies({
            url: environmentConfig.authority
          });
        } catch (domainError) {
          AuthLogger.warn('Could not clear domain-specific cookies:', domainError);
        }
      } else {
        AuthLogger.warn('CapacitorCookies plugin not available, cannot clear browser cookies');
      }

      // Force logout from OAuth2 provider by opening logout URL briefly
      await this.forceOAuth2Logout();

      AuthLogger.log('✅ [AUTH] Browser session cleared successfully');
    } catch (error) {
      AuthLogger.error('Error clearing browser session:', error);
      // Don't throw error here - authentication can still proceed
      // The user will just need to manually log out if cached credentials exist
    }
  }

  /**
   * Force logout from OAuth2 provider to clear server-side session
   * Uses a hidden iframe approach to avoid disrupting user experience
   */
  private static async forceOAuth2Logout(): Promise<void> {
    try {
      AuthLogger.log('🚪 [AUTH] Forcing logout from OAuth2 provider');

      // Build logout URL - most OIDC providers support end_session_endpoint
      const logoutUrl = `${environmentConfig.authority}/connect/endsession?post_logout_redirect_uri=${encodeURIComponent(environmentConfig.nativeBaseUrl)}`;

      AuthLogger.log('🌐 [AUTH] Performing silent logout to clear server session');

      // Use native HTTP to perform a silent logout request
      // This avoids opening a visible browser window
      if (NativeHttp.isNativeHttpAvailable()) {
        try {
          await NativeHttp.request({
            url: logoutUrl,
            method: 'GET',
            headers: {},
            timeout: 5000 // 5 second timeout
          });
          AuthLogger.log('✅ [AUTH] Silent logout completed via HTTP request');
        } catch (httpError) {
          AuthLogger.warn('Silent logout via HTTP failed, trying browser approach:', httpError);
          await this.fallbackBrowserLogout(logoutUrl);
        }
      } else {
        AuthLogger.log('Native HTTP not available, using browser logout');
        await this.fallbackBrowserLogout(logoutUrl);
      }

    } catch (error) {
      AuthLogger.warn('Could not force OAuth2 logout:', error);
      // Don't throw - this is a best-effort cleanup
    }
  }

  /**
   * Fallback browser-based logout when HTTP approach fails
   */
  private static async fallbackBrowserLogout(logoutUrl: string): Promise<void> {
    try {
      // Open logout URL in system browser briefly to clear server-side session
      await InAppBrowser.openInSystemBrowser({
        url: logoutUrl,
        options: DefaultSystemBrowserOptions
      });

      // Close the browser after a short delay to allow logout to complete
      setTimeout(async () => {
        try {
          await InAppBrowser.close();
          AuthLogger.log('✅ [AUTH] Logout browser closed');
        } catch (closeError) {
          AuthLogger.info('Logout browser already closed or error closing:', closeError);
        }
      }, 1500); // 1.5 second delay to ensure logout completes

    } catch (error) {
      AuthLogger.warn('Fallback browser logout failed:', error);
    }
  }

  /**
   * Clean up authentication state and promises
   */
  private static cleanup(): void {
    console.log('🧹 [AUTH] Cleaning up authentication state');
    this.authInProgress = false;
    this.authPromiseResolve = null;
    this.authPromiseReject = null;

    // Note: Don't remove deep link listener here as it should persist for future auth attempts
    // The listener is only removed when the service is destroyed
  }

  /**
   * Get current authenticated user
   */
  static async getCurrentUser(): Promise<AuthResult | null> {
    try {
      const result = await Preferences.get({ key: 'auth_tokens' });

      if (!result.value) {
        return null;
      }

      const storedAuth = JSON.parse(result.value);

      // Check if token is expired
      if (Date.now() >= storedAuth.expiresAt) {
        debugLog('CapacitorAuthService - Stored token is expired');
        await this.signOut();
        return null;
      }

      return {
        accessToken: storedAuth.accessToken,
        idToken: storedAuth.idToken,
        refreshToken: storedAuth.refreshToken,
        expiresIn: Math.floor((storedAuth.expiresAt - Date.now()) / 1000),
        profile: storedAuth.profile
      };
    } catch (error) {
      debugLog('CapacitorAuthService - Error getting current user:', error);
      return null;
    }
  }

  /**
   * Sign out user and clear stored tokens
   */
  static async signOut(): Promise<void> {
    try {
      debugLog('CapacitorAuthService - Signing out user');

      // Clear browser session data first
      await this.clearBrowserSession();

      // Clear stored tokens
      await Preferences.remove({ key: 'auth_tokens' });

      // Clear any pending auth states
      const keys = await Preferences.keys();
      for (const key of keys.keys) {
        if (key.startsWith('auth_state_')) {
          await Preferences.remove({ key });
        }
      }

      // Clean up any pending authentication
      this.cleanup();

      debugLog('CapacitorAuthService - Sign out completed');
    } catch (error) {
      debugLog('CapacitorAuthService - Error during sign out:', error);
      throw error;
    }
  }

  /**
   * Check if authentication is currently in progress
   */
  static isAuthInProgress(): boolean {
    return this.authInProgress;
  }

  /**
   * Debug helper to check authentication state
   */
  static async debugAuthState(): Promise<void> {
    console.log('🔍 [DEBUG] Current authentication state:');
    console.log('  - Auth in progress:', this.authInProgress);
    console.log('  - Has promise resolver:', !!this.authPromiseResolve);
    console.log('  - Has promise rejector:', !!this.authPromiseReject);
    console.log('  - Has deep link listener:', !!this.deepLinkListener);

    try {
      const currentUser = await this.getCurrentUser();
      console.log('  - Current user:', currentUser ? {
        userId: currentUser.profile.sub,
        userName: currentUser.profile.name,
        userEmail: currentUser.profile.email,
        tokenExpiry: new Date(Date.now() + (currentUser.expiresIn * 1000)).toISOString()
      } : 'None');
    } catch (error) {
      console.log('  - Error getting current user:', error);
    }

    try {
      const keys = await Preferences.keys();
      const authKeys = keys.keys.filter(key => key.startsWith('auth_'));
      console.log('  - Stored auth keys:', authKeys);
    } catch (error) {
      console.log('  - Error getting stored keys:', error);
    }
  }
}
