/* For information on how to create your own theme, please see:
http://ionicframework.com/docs/theming/ */
@layer theme, base, components, utilities;
@import "tailwindcss/theme.css" layer(theme) important;
@import "tailwindcss/utilities.css" layer(utilities) important;

@utility container {
  margin-inline: auto;
}

:root {
  --ion-color-primary: #3880ff;
  --ion-color-primary-rgb: 56, 128, 255;
  --ion-color-primary-contrast: #ffffff;
  --ion-color-primary-contrast-rgb: 255, 255, 255;
  --ion-color-primary-shade: #3171e0;
  --ion-color-primary-tint: #4c8dff;

  --ion-color-secondary: #35A192;
  --ion-color-secondary-rgb: 53, 161, 146;
  --ion-color-secondary-contrast: #ffffff;
  --ion-color-secondary-contrast-rgb: 255, 255, 255;
  --ion-color-secondary-shade: #2f8e80;
  --ion-color-secondary-tint: #49aa9d;

  --ion-background-color: #ffffff;
  --ion-background-color-rgb: 255, 255, 255;
  --ion-text-color: #000000;
  --ion-text-color-rgb: 0, 0, 0;
  --ion-border-color: #d7d8da;

  /* Enhanced theme variables for better dark mode support */
  --ion-card-background: #ffffff;
  --ion-toolbar-background: #ffffff;
  --ion-item-background: #ffffff;

  /* Transition variables */
  --theme-transition-duration: 0.3s;
  --theme-transition-timing: ease;
}


.ion-color-secondary {
  --ion-color-base: var(--ion-color-secondary);
  --ion-color-base-rgb: var(--ion-color-secondary-rgb);
  --ion-color-contrast: var(--ion-color-secondary-contrast);
  --ion-color-contrast-rgb: var(--ion-color-secondary-contrast-rgb);
  --ion-color-shade: var(--ion-color-secondary-shade);
  --ion-color-tint: var(--ion-color-secondary-tint);
}

/* Dark mode variables */
.ion-palette-dark {
  --ion-card-background: #1e1e1e;
  --ion-toolbar-background: #1f1f1f;
  --ion-item-background: #1e1e1e;

  --ion-background-color: #121212;
  --ion-background-color-rgb: 18, 18, 18;
  --ion-text-color: #ffffff;
  --ion-text-color-rgb: 255, 255, 255;
  --ion-border-color: #333333;

  /* Enhanced color variables for better contrast */
  --ion-color-light: #2a2a2a;
  --ion-color-light-rgb: 42, 42, 42;
  --ion-color-light-contrast: #ffffff;
  --ion-color-light-contrast-rgb: 255, 255, 255;
  --ion-color-light-shade: #252525;
  --ion-color-light-tint: #404040;

  --ion-color-medium: #9d9d9d;
  --ion-color-medium-rgb: 157, 157, 157;
  --ion-color-medium-contrast: #000000;
  --ion-color-medium-contrast-rgb: 0, 0, 0;
  --ion-color-medium-shade: #8a8a8a;
  --ion-color-medium-tint: #a7a7a7;

  --ion-color-dark: #f4f4f4;
  --ion-color-dark-rgb: 244, 244, 244;
  --ion-color-dark-contrast: #000000;
  --ion-color-dark-contrast-rgb: 0, 0, 0;
  --ion-color-dark-shade: #d7d7d7;
  --ion-color-dark-tint: #f5f5f5;

  --ion-color-step-50: #1e1e1e;
  --ion-color-step-100: #2a2a2a;
  --ion-color-step-150: #363636;
  --ion-color-step-200: #414141;
  --ion-color-step-250: #4d4d4d;
  --ion-color-step-300: #595959;
  --ion-color-step-350: #656565;
  --ion-color-step-400: #717171;
  --ion-color-step-450: #7d7d7d;
  --ion-color-step-500: #898989;
  --ion-color-step-550: #949494;
  --ion-color-step-600: #a0a0a0;
  --ion-color-step-650: #acacac;
  --ion-color-step-700: #b8b8b8;
  --ion-color-step-750: #c4c4c4;
  --ion-color-step-800: #d0d0d0;
  --ion-color-step-850: #dbdbdb;
  --ion-color-step-900: #e7e7e7;
  --ion-color-step-950: #f3f3f3;
}

/* Global smooth transitions for theme changes */
* {
  transition:
    background-color var(--theme-transition-duration) var(--theme-transition-timing),
    color var(--theme-transition-duration) var(--theme-transition-timing),
    border-color var(--theme-transition-duration) var(--theme-transition-timing),
    box-shadow var(--theme-transition-duration) var(--theme-transition-timing);
}

/* Disable transitions during theme initialization to prevent flash */
.theme-transitioning,
.theme-transitioning * {
  transition: none !important;
}

/* Enhanced component theming */
ion-card {
  --background: var(--ion-card-background);
  transition:
    background var(--theme-transition-duration) var(--theme-transition-timing),
    box-shadow var(--theme-transition-duration) var(--theme-transition-timing);
}

ion-toolbar {
  --background: var(--ion-toolbar-background);
  transition: background var(--theme-transition-duration) var(--theme-transition-timing);
}

ion-item {
  --background: var(--ion-item-background);
  transition: background var(--theme-transition-duration) var(--theme-transition-timing);
}

/* Improved focus styles for accessibility */
ion-button:focus,
ion-toggle:focus,
ion-input:focus {
  outline: 2px solid var(--ion-color-primary);
  outline-offset: 2px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
    animation: none !important;
  }
}