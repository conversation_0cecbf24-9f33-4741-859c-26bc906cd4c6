import React from "react";
import ReactD<PERSON> from "react-dom/client";
import { AuthProvider } from "react-oidc-context";
import { userManager, platformInfo, debugLog } from "./config/user-manager.config";
import { AuthStorageService } from "./services/auth-storage.service";
import App from "./App";

// OIDC configuration for production authentication
const authProviderConfig = {
  // Use our custom UserManager with PKCE
  userManager: userManager,

  // Don't handle signin callback here - let CallbackPage handle it
  // This prevents conflicts with manual callback processing
  onSigninCallback: undefined,

  // Callback for silent token renewal
  onSigninSilentCallback: async (user: any) => {
    try {
      if (user) {
        await AuthStorageService.storeTokens(user);
        debugLog("main.tsx - Silent token renewal completed");
      }
    } catch (error) {
      console.error("❌ main.tsx - Error in silent signin callback:", error);
    }
  },

  // Callback after OIDC logout
  onSignoutCallback: async () => {
    try {
      await AuthStorageService.clearTokens();
      debugLog("main.tsx - OIDC logout callback completed");
    } catch (error) {
      console.error("❌ main.tsx - Error in signout callback:", error);
    }
  },

  // User management callbacks
  onUserLoaded: (user: any) => {
    debugLog("main.tsx - OIDC user loaded", user.profile?.email);
  },

  onUserUnloaded: () => {
    debugLog("main.tsx - OIDC user unloaded");
  },

  // Token lifecycle callbacks
  onAccessTokenExpiring: () => {
    debugLog("main.tsx - Access token expiring, attempting renewal...");
  },

  onAccessTokenExpired: () => {
    debugLog("main.tsx - Access token expired");
  },

  onSilentRenewError: (error: any) => {
    console.error("❌ main.tsx - Silent token renewal failed:", error);
  }
};

// Global error handling
window.addEventListener('error', (event) => {
  console.error('❌ Global error:', event.error);
  debugLog('Global error caught:', {
    message: event.error?.message,
    filename: event.filename,
    lineno: event.lineno,
    colno: event.colno,
    stack: event.error?.stack
  });
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('❌ Unhandled promise rejection:', event.reason);
  debugLog('Unhandled promise rejection:', {
    reason: event.reason,
    promise: event.promise
  });

  // Prevent the default browser behavior (logging to console)
  event.preventDefault();
});

// Validate configuration on startup
import { ConfigValidator } from "./utils/config-validator";
const configValidation = ConfigValidator.validateAndLog();

if (!configValidation.isValid) {
  console.error("❌ Critical configuration errors detected. Authentication may not work properly.");
}

// Initialize debug configuration for Chrome DevTools remote debugging
import { DebugConfig } from "./config/debug.config";
import { Capacitor } from '@capacitor/core';
DebugConfig.initialize();

// Test console logging and crypto for remote debugging
if (Capacitor.isNativePlatform()) {
  console.log('🔧 [MAIN] Testing console logging for Chrome DevTools remote debugging');
  DebugConfig.testConsoleLogging();

  // Test crypto functionality early
  import('./utils/crypto-fallback').then(({ CryptoFallback }) => {
    CryptoFallback.testCrypto().then(() => {
      console.log('✅ [MAIN] Crypto functionality test passed during startup');
    }).catch((error) => {
      console.error('❌ [MAIN] Crypto functionality test failed during startup:', error);
    });
  });

  // Test official Capacitor HTTP functionality early
  import('./utils/native-http').then(({ NativeHttp }) => {
    NativeHttp.testHttp().then(() => {
      console.log('✅ [MAIN] Official Capacitor HTTP functionality test passed during startup');
    }).catch((error) => {
      console.warn('⚠️ [MAIN] Official Capacitor HTTP functionality test failed during startup (will use fetch fallback):', error);
    });
  });

  // Start navigation monitoring for debugging
  import('./utils/navigation-debug').then(({ NavigationDebug }) => {
    NavigationDebug.startNavigationMonitoring();
    NavigationDebug.testNavigation();
    console.log('🧭 [MAIN] Navigation debugging initialized');
  });
}

// Initialize modern Capacitor authentication service for native platforms
import { CapacitorAuthService } from "./services/capacitor-auth.service";
CapacitorAuthService.initialize().then(() => {
  console.log("✅ [MAIN] Modern Capacitor authentication service initialized successfully");
  debugLog("Modern Capacitor authentication service initialized successfully");
}).catch((error) => {
  console.error("❌ [MAIN] Failed to initialize Capacitor authentication service:", error);
});

// Startup information
debugLog("Starting Agenda Familiar App with Santillana Connect OIDC", platformInfo);

ReactDOM.createRoot(document.getElementById("root")!).render(
  <AuthProvider {...authProviderConfig}>
    <App />
  </AuthProvider>
);