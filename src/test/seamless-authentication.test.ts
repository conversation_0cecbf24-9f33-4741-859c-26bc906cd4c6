/**
 * Comprehensive tests for the new seamless authentication system
 * Tests security, performance, and user experience improvements
 */

import { CapacitorAuthService } from '../services/capacitor-auth.service';
import { Capacitor, CapacitorCookies } from '@capacitor/core';
import { InAppBrowser } from '@capacitor/inappbrowser';
import { Preferences } from '@capacitor/preferences';

// Mock Capacitor plugins
jest.mock('@capacitor/core');
jest.mock('@capacitor/inappbrowser');
jest.mock('@capacitor/preferences');

const mockCapacitor = Capacitor as jest.Mocked<typeof Capacitor>;
const mockCapacitorCookies = CapacitorCookies as jest.Mocked<typeof CapacitorCookies>;
const mockInAppBrowser = InAppBrowser as jest.Mocked<typeof InAppBrowser>;
const mockPreferences = Preferences as jest.Mocked<typeof Preferences>;

describe('Seamless Authentication System', () => {
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Setup default mock implementations
    mockCapacitor.isNativePlatform.mockReturnValue(true);
    mockCapacitor.isPluginAvailable.mockReturnValue(true);
    mockCapacitorCookies.clearAllCookies.mockResolvedValue();
    mockCapacitorCookies.clearCookies.mockResolvedValue();
    mockInAppBrowser.openInWebView.mockResolvedValue();
    mockInAppBrowser.openInSystemBrowser.mockResolvedValue();
    mockInAppBrowser.close.mockResolvedValue();
    mockPreferences.remove.mockResolvedValue();
    mockPreferences.get.mockResolvedValue({ value: null });
  });

  describe('Seamless Authentication Mode', () => {
    test('should enable seamless authentication by default', () => {
      expect(CapacitorAuthService.isSeamlessAuthEnabled()).toBe(true);
    });

    test('should allow toggling seamless authentication mode', () => {
      CapacitorAuthService.setSeamlessAuthMode(false);
      expect(CapacitorAuthService.isSeamlessAuthEnabled()).toBe(false);

      CapacitorAuthService.setSeamlessAuthMode(true);
      expect(CapacitorAuthService.isSeamlessAuthEnabled()).toBe(true);
    });
  });

  describe('Session Isolation', () => {
    test('should perform complete session isolation before authentication', async () => {
      // Mock successful authentication flow
      const mockAuthResult = {
        accessToken: 'test-access-token',
        idToken: 'test-id-token',
        refreshToken: 'test-refresh-token',
        expiresIn: 3600,
        profile: {
          sub: 'test-user-id',
          name: 'Test User',
          email: '<EMAIL>'
        }
      };

      // Setup mocks for authentication flow
      mockPreferences.get.mockResolvedValueOnce({ value: null }); // No existing auth state
      
      // Start authentication (this should trigger session isolation)
      const authPromise = CapacitorAuthService.signIn();

      // Verify session isolation was performed
      expect(mockCapacitorCookies.clearAllCookies).toHaveBeenCalled();
      expect(mockCapacitorCookies.clearCookies).toHaveBeenCalledWith({
        url: expect.stringContaining('santillanaconnect.com')
      });
      expect(mockInAppBrowser.close).toHaveBeenCalled();
      expect(mockPreferences.remove).toHaveBeenCalledWith({ key: 'auth_state' });
    });

    test('should clear browser cookies for OAuth2 provider domain', async () => {
      // Trigger session isolation
      const authPromise = CapacitorAuthService.signIn();

      // Verify domain-specific cookie clearing
      expect(mockCapacitorCookies.clearCookies).toHaveBeenCalledWith({
        url: expect.stringContaining('pre-identity.santillanaconnect.com')
      });
    });

    test('should handle session isolation failures gracefully', async () => {
      // Mock cookie clearing failure
      mockCapacitorCookies.clearAllCookies.mockRejectedValueOnce(new Error('Cookie clearing failed'));

      // Authentication should still proceed
      const authPromise = CapacitorAuthService.signIn();

      // Should not throw error due to session isolation failure
      expect(authPromise).not.toReject();
    });
  });

  describe('Seamless WebView Authentication', () => {
    test('should use seamless WebView when enabled', async () => {
      CapacitorAuthService.setSeamlessAuthMode(true);
      
      const authPromise = CapacitorAuthService.signIn();

      // Should use openInWebView instead of openInSystemBrowser
      expect(mockInAppBrowser.openInWebView).toHaveBeenCalled();
      expect(mockInAppBrowser.openInSystemBrowser).not.toHaveBeenCalled();
    });

    test('should use system browser when seamless mode is disabled', async () => {
      CapacitorAuthService.setSeamlessAuthMode(false);
      
      const authPromise = CapacitorAuthService.signIn();

      // Should use openInSystemBrowser instead of openInWebView
      expect(mockInAppBrowser.openInSystemBrowser).toHaveBeenCalled();
      expect(mockInAppBrowser.openInWebView).not.toHaveBeenCalled();
    });

    test('should fallback to system browser if seamless WebView fails', async () => {
      CapacitorAuthService.setSeamlessAuthMode(true);
      
      // Mock WebView failure
      mockInAppBrowser.openInWebView.mockRejectedValueOnce(new Error('WebView failed'));
      
      const authPromise = CapacitorAuthService.signIn();

      // Should fallback to system browser
      expect(mockInAppBrowser.openInWebView).toHaveBeenCalled();
      expect(mockInAppBrowser.openInSystemBrowser).toHaveBeenCalled();
    });

    test('should use correct seamless WebView options', async () => {
      CapacitorAuthService.setSeamlessAuthMode(true);
      
      const authPromise = CapacitorAuthService.signIn();

      expect(mockInAppBrowser.openInWebView).toHaveBeenCalledWith({
        url: expect.any(String),
        options: expect.objectContaining({
          showURL: false,
          showToolbar: false,
          clearCache: true,
          clearSessionCache: true,
          allowInlineMediaPlayback: false,
          mediaPlaybackRequiresUserAction: true,
          enableViewportScale: false,
          allowOverscroll: false
        })
      });
    });
  });

  describe('Enhanced System Browser Fallback', () => {
    test('should use enhanced system browser options', async () => {
      CapacitorAuthService.setSeamlessAuthMode(false);
      
      const authPromise = CapacitorAuthService.signIn();

      expect(mockInAppBrowser.openInSystemBrowser).toHaveBeenCalledWith({
        url: expect.any(String),
        options: expect.objectContaining({
          clearCache: true,
          clearSessionCache: true,
          showTitle: false,
          hideUrlBar: true,
          hideNavigationButtons: true
        })
      });
    });
  });

  describe('Security Validation', () => {
    test('should validate session clearing before authentication', async () => {
      // Mock successful session clearing validation
      mockPreferences.get.mockResolvedValueOnce({ value: null }); // Auth state cleared
      
      const authPromise = CapacitorAuthService.signIn();

      // Should check if auth state was properly cleared
      expect(mockPreferences.get).toHaveBeenCalledWith({ key: 'auth_state' });
    });

    test('should proceed with authentication even if validation fails', async () => {
      // Mock validation failure (auth state still present)
      mockPreferences.get.mockResolvedValueOnce({ value: 'some-auth-state' });
      
      const authPromise = CapacitorAuthService.signIn();

      // Should still proceed with authentication
      expect(authPromise).not.toReject();
    });

    test('should force fresh authentication on each signIn call', async () => {
      // Call signIn multiple times
      const auth1 = CapacitorAuthService.signIn();
      
      // Each call should perform session isolation
      expect(mockCapacitorCookies.clearAllCookies).toHaveBeenCalledTimes(1);
      
      // Reset and call again
      jest.clearAllMocks();
      mockCapacitor.isNativePlatform.mockReturnValue(true);
      mockCapacitor.isPluginAvailable.mockReturnValue(true);
      
      const auth2 = CapacitorAuthService.signIn();
      
      expect(mockCapacitorCookies.clearAllCookies).toHaveBeenCalledTimes(1);
    });
  });

  describe('Enhanced Credential Validation Tests', () => {
    test('should validate credential freshness before authentication', async () => {
      // Mock credential freshness validation to return true (no cached data)
      const mockValidateCredentialFreshness = jest.spyOn(
        (CapacitorAuthService as any).SessionIsolationManager || CapacitorAuthService as any,
        'validateCredentialFreshness'
      ).mockResolvedValue(true);

      const authPromise = CapacitorAuthService.signIn();

      // Verify credential freshness validation was called
      expect(mockValidateCredentialFreshness).toHaveBeenCalled();
    });

    test('should perform additional cleanup when cached credentials detected', async () => {
      // Mock credential validation to fail (cached credentials detected)
      jest.spyOn(
        (CapacitorAuthService as any).SessionIsolationManager || CapacitorAuthService as any,
        'validateCredentialFreshness'
      ).mockResolvedValue(false);

      // Mock additional cleanup method
      const mockPerformAdditionalCleanup = jest.spyOn(
        CapacitorAuthService as any,
        'performAdditionalCleanup'
      ).mockResolvedValue(undefined);

      const authPromise = CapacitorAuthService.signIn();

      // Verify additional cleanup was called when cached credentials detected
      expect(mockPerformAdditionalCleanup).toHaveBeenCalled();
    });

    test('should validate authentication timing to prevent replay attacks', async () => {
      const currentTime = Date.now();

      // Mock auth start time to be recent
      (CapacitorAuthService as any).authStartTime = currentTime;
      (CapacitorAuthService as any).authInProgress = true;

      // Simulate callback within valid timeframe (under 5 minutes)
      const callbackUrl = 'capacitor://localhost/callback?code=test-code&state=test-state';

      // Mock auth state retrieval
      jest.spyOn(CapacitorAuthService as any, 'retrieveAuthState')
        .mockResolvedValue({
          state: 'test-state',
          codeVerifier: 'test-verifier',
          nonce: 'test-nonce',
          timestamp: currentTime
        });

      // Should not throw timing error
      await expect((CapacitorAuthService as any).handleAuthCallback(callbackUrl))
        .not.toThrow('Callback received too late');
    });

    test('should reject callbacks that arrive too late', async () => {
      const oldTime = Date.now() - 400000; // 6+ minutes ago

      // Mock auth start time to be old
      (CapacitorAuthService as any).authStartTime = oldTime;
      (CapacitorAuthService as any).authInProgress = true;

      // Simulate late callback
      const callbackUrl = 'capacitor://localhost/callback?code=test-code&state=test-state';

      // Should reject due to timeout
      await (CapacitorAuthService as any).handleAuthCallback(callbackUrl);

      // Should have cleaned up due to timeout
      expect((CapacitorAuthService as any).authInProgress).toBe(false);
    });

    test('should validate token freshness in final validation', async () => {
      const currentTime = Math.floor(Date.now() / 1000);
      const mockAuthState = {
        state: 'test-state',
        codeVerifier: 'test-verifier',
        nonce: 'test-nonce',
        timestamp: Date.now()
      };

      // Mock fresh access token (issued recently)
      const freshTokenPayload = {
        sub: 'user123',
        iat: currentTime - 60, // 1 minute ago (fresh)
        exp: currentTime + 3600
      };

      jest.spyOn(CapacitorAuthService as any, 'parseJwtPayload')
        .mockReturnValueOnce(freshTokenPayload) // For access token freshness check
        .mockReturnValueOnce({ // For ID token validation
          sub: 'user123',
          name: 'Test User',
          email: '<EMAIL>',
          nonce: 'test-nonce',
          iss: 'https://pre-identity.santillanaconnect.com',
          aud: 'sumun_office_co_pre'
        });

      // Mock token exchange
      jest.spyOn(CapacitorAuthService as any, 'exchangeCodeForTokens')
        .mockResolvedValue({
          access_token: 'fresh-access-token',
          id_token: 'id-token',
          refresh_token: 'refresh-token',
          expires_in: 3600
        });

      // Should succeed with fresh token
      const result = await (CapacitorAuthService as any).processAuthCallback(
        'test-code',
        'test-state',
        mockAuthState
      );

      expect(result).toBeDefined();
      expect(result.accessToken).toBe('fresh-access-token');
    });

    test('should reject cached/old tokens in final validation', async () => {
      const currentTime = Math.floor(Date.now() / 1000);
      const mockAuthState = {
        state: 'test-state',
        codeVerifier: 'test-verifier',
        nonce: 'test-nonce',
        timestamp: Date.now()
      };

      // Mock old access token (issued too long ago)
      const oldTokenPayload = {
        sub: 'user123',
        iat: currentTime - 400, // 6+ minutes ago (too old)
        exp: currentTime + 3600
      };

      jest.spyOn(CapacitorAuthService as any, 'parseJwtPayload')
        .mockReturnValueOnce(oldTokenPayload); // For access token freshness check

      // Mock token exchange
      jest.spyOn(CapacitorAuthService as any, 'exchangeCodeForTokens')
        .mockResolvedValue({
          access_token: 'old-access-token',
          id_token: 'id-token',
          refresh_token: 'refresh-token',
          expires_in: 3600
        });

      // Should reject due to old token
      await expect((CapacitorAuthService as any).processAuthCallback(
        'test-code',
        'test-state',
        mockAuthState
      )).rejects.toThrow('Access token appears to be cached');
    });
  });

  describe('Performance Optimizations', () => {
    test('should minimize network requests during session isolation', async () => {
      const authPromise = CapacitorAuthService.signIn();

      // Should perform efficient session clearing
      expect(mockCapacitorCookies.clearAllCookies).toHaveBeenCalledTimes(1);
      expect(mockCapacitorCookies.clearCookies).toHaveBeenCalledTimes(1);
      expect(mockInAppBrowser.close).toHaveBeenCalledTimes(1);
    });

    test('should handle concurrent authentication attempts', async () => {
      const auth1Promise = CapacitorAuthService.signIn();
      
      // Second authentication attempt should be rejected
      await expect(CapacitorAuthService.signIn()).rejects.toThrow('Authentication already in progress');
    });
  });

  describe('Cross-Platform Compatibility', () => {
    test('should work on iOS platform', async () => {
      mockCapacitor.getPlatform.mockReturnValue('ios');
      
      const authPromise = CapacitorAuthService.signIn();

      expect(mockInAppBrowser.openInWebView).toHaveBeenCalled();
    });

    test('should work on Android platform', async () => {
      mockCapacitor.getPlatform.mockReturnValue('android');
      
      const authPromise = CapacitorAuthService.signIn();

      expect(mockInAppBrowser.openInWebView).toHaveBeenCalled();
    });

    test('should reject on web platform', async () => {
      mockCapacitor.isNativePlatform.mockReturnValue(false);
      
      await expect(CapacitorAuthService.signIn()).rejects.toThrow('CapacitorAuthService is only for native platforms');
    });
  });

  describe('Error Handling', () => {
    test('should handle WebView opening failures', async () => {
      mockInAppBrowser.openInWebView.mockRejectedValue(new Error('WebView failed'));
      mockInAppBrowser.openInSystemBrowser.mockRejectedValue(new Error('System browser failed'));
      
      await expect(CapacitorAuthService.signIn()).rejects.toThrow('Failed to open authentication browser');
    });

    test('should cleanup on authentication errors', async () => {
      mockInAppBrowser.openInWebView.mockRejectedValue(new Error('WebView failed'));
      mockInAppBrowser.openInSystemBrowser.mockRejectedValue(new Error('System browser failed'));
      
      try {
        await CapacitorAuthService.signIn();
      } catch (error) {
        // Should cleanup authentication state
        expect(CapacitorAuthService.isSeamlessAuthEnabled()).toBeDefined();
      }
    });
  });
});
