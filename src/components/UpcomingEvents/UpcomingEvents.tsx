import React from 'react';

interface Event {
  id: string;
  type: 'task' | 'evaluation' | 'event';
  title: string;
  description: string;
  date: string;
}

interface UpcomingEventsProps {
  events: Event[];
}

const getTypeStyles = (type: 'task' | 'evaluation' | 'event') => {
  switch (type) {
    case 'task':
      return {
        bg: 'bg-info/10',
        text: 'text-info',
      };
    case 'evaluation':
      return {
        bg: 'bg-warning/10',
        text: 'text-warning',
      };
    case 'event':
      return {
        bg: 'bg-success/10',
        text: 'text-success',
      };
    default:
      return {
        bg: 'bg-base-200',
        text: 'text-base-content',
      };
  }
};

const UpcomingEvents: React.FC<UpcomingEventsProps> = ({ events }) => {
  const eventCounts = {
    task: events.filter(e => e.type === 'task').length,
    evaluation: events.filter(e => e.type === 'evaluation').length,
    event: events.filter(e => e.type === 'event').length,
  };

  return (
    <div className="bg-white rounded-xl p-5 shadow-lg mb-6">
      <h3 className="text-lg font-semibold text-gray-700 mb-4">Próximos Eventos</h3>

      <div className="flex gap-4 mb-4">
        <div className="flex items-center gap-2">
          <span className="text-info font-semibold text-sm">
            {eventCounts.task} Tareas
          </span>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-warning font-semibold text-sm">
            {eventCounts.evaluation} Evaluaciones
          </span>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-success font-semibold text-sm">
            {eventCounts.event} Eventos
          </span>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {events.map((event) => {
          const styles = getTypeStyles(event.type);
          return (
            <div
              key={event.id}
              className={`${styles.bg} rounded-lg p-4 cursor-pointer hover:-translate-y-0.5 transition-transform duration-200`}
            >
              <div className="flex justify-between items-start mb-2">
                <span className={`text-xs font-semibold uppercase ${styles.text}`}>
                  {event.type}
                </span>
                <span className="text-xs text-gray-500">{event.date}</span>
              </div>
              <h4 className="text-base font-semibold text-gray-700 mb-2">{event.title}</h4>
              <p className="text-sm text-gray-600 line-clamp-2">{event.description}</p>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default UpcomingEvents; 