import React from 'react';

interface AttendanceDay {
  date: string;
  morning: 'present' | 'late' | 'justified' | 'unjustified';
  afternoon: 'present' | 'late' | 'justified' | 'unjustified';
}

interface AttendanceSectionProps {
  attendance: AttendanceDay[];
}

const getStatusColor = (status: 'present' | 'late' | 'justified' | 'unjustified') => {
  switch (status) {
    case 'present': return 'bg-success';
    case 'late': return 'bg-warning';
    case 'justified': return 'bg-info';
    case 'unjustified': return 'bg-error';
    default: return 'bg-gray-300';
  }
};

export const AttendanceSection: React.FC<AttendanceSectionProps> = ({ attendance }) => {
  return (
    <div className="bg-white rounded-xl p-5 shadow-lg mb-6">
      <h3 className="text-lg font-semibold text-gray-700 mb-4">Asistencia</h3>
      <div className="grid grid-cols-5 gap-3">
        {attendance.map((day, index) => (
          <div key={index} className="bg-base-200 rounded-lg p-3 text-center">
            <div className="text-sm text-gray-600 mb-2">{day.date}</div>
            <div className="text-xs text-gray-700">AM</div>
            <div className={`w-3 h-3 rounded-full mx-auto my-1 ${getStatusColor(day.morning)}`} />
            <div className="text-xs text-gray-700">PM</div>
            <div className={`w-3 h-3 rounded-full mx-auto my-1 ${getStatusColor(day.afternoon)}`} />
          </div>
        ))}
      </div>
    </div>
  );
};

export default AttendanceSection; 