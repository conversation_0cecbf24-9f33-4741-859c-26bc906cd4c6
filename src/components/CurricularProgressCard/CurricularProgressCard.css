/* Curricular Progress Card - Native Ionic Styling */
.curricular-progress-card {
  --background: var(--ion-card-background);
  --color: var(--ion-card-color);
  margin-bottom: 1.5rem;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.curricular-progress-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.progress-header {
  --background: transparent;
  --color: inherit;
  padding-bottom: 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.progress-title {
  --color: var(--ion-text-color);
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
}

.info-button {
  --color: var(--ion-color-medium);
  --padding-start: 8px;
  --padding-end: 8px;
  --border-radius: 50%;
  width: 32px;
  height: 32px;
}

.progress-content {
  --background: transparent;
  --color: inherit;
  padding-top: 0;
}

.subject-section {
  margin-bottom: 1.5rem;
}

.subject-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.75rem;
}

.subject-title {
  color: var(--ion-text-color);
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
}

.details-button {
  --color: var(--ion-color-medium);
  --padding-start: 8px;
  --padding-end: 8px;
  --border-radius: 50%;
  width: 28px;
  height: 28px;
}

.experience-text {
  color: var(--ion-color-medium);
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0;
}

.progress-sequence {
  margin-bottom: 1.5rem;
}

.sequence-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.sequence-text {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--ion-color-medium-shade);
}

.sequence-divider {
  flex: 1;
  height: 1px;
  background: linear-gradient(to right,
    color-mix(in srgb, var(--ion-color-primary) 30%, transparent),
    transparent);
}

.nav-button {
  --padding-start: 8px;
  --padding-end: 8px;
  width: 32px;
  height: 32px;
}

/* Loading skeleton styles */
.loading-skeleton {
  padding: 1rem;
}

.skeleton-title {
  height: 1.5rem;
  background: var(--ion-color-light);
  border-radius: 4px;
  margin-bottom: 1rem;
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.skeleton-subtitle {
  height: 1rem;
  background: var(--ion-color-light);
  border-radius: 4px;
  margin-bottom: 1.5rem;
  width: 75%;
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.skeleton-progress {
  height: 5rem;
  background: var(--ion-color-light);
  border-radius: 12px;
  margin-bottom: 1rem;
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.skeleton-stats {
  display: flex;
  gap: 1rem;
}

.skeleton-stat {
  height: 2rem;
  background: var(--ion-color-light);
  border-radius: 4px;
  flex: 1;
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.progress-header {
  border-bottom: 1px solid var(--ion-border-color);
  padding-bottom: 1rem;
}

.subject-section {
  position: relative;
}

.subject-section::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--ion-border-color), transparent);
}

.progress-sequence {
  position: relative;
}

.progress-visualization {
  background: var(--ion-color-step-100);
  border-radius: 0.75rem;
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
}

.progress-visualization::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--ion-color-secondary-tint) 0%, var(--ion-color-primary-tint) 100%);
  opacity: 0.1;
  pointer-events: none;
}

.progress-step {
  position: relative;
  z-index: 1;
}

.step-icon {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px var(--ion-color-step-200);
}

.progress-step.completed .step-icon {
  animation: pulse-success 2s ease-in-out infinite;
}

.progress-step.in-progress .step-icon {
  animation: pulse-warning 2s ease-in-out infinite;
}

@keyframes pulse-success {
  0%, 100% {
    box-shadow: 0 2px 8px var(--ion-color-step-200), 0 0 0 0 var(--ion-color-success-tint);
  }
  50% {
    box-shadow: 0 2px 8px var(--ion-color-step-200), 0 0 0 8px transparent;
  }
}

@keyframes pulse-warning {
  0%, 100% {
    box-shadow: 0 2px 8px var(--ion-color-step-200), 0 0 0 0 var(--ion-color-warning-tint);
  }
  50% {
    box-shadow: 0 2px 8px var(--ion-color-step-200), 0 0 0 8px transparent;
  }
}

.connection-line {
  transition: all 0.3s ease;
  border-radius: 1px;
}

.progress-stats {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--ion-border-color);
}

.stat-item {
  background: var(--ion-color-step-50);
  padding: 0.5rem 0.75rem;
  border-radius: 1rem;
  transition: all 0.2s ease;
}

.stat-item:hover {
  background: var(--ion-color-step-100);
  transform: translateY(-1px);
}

.stat-item .w-4.h-4 {
  box-shadow: 0 1px 3px var(--ion-color-step-200);
}

/* Enhanced button styling */
.curricular-progress-card .btn-circle {
  box-shadow: 0 2px 8px var(--ion-color-step-200);
  transition: all 0.3s ease;
}

.curricular-progress-card .btn-circle:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px var(--ion-color-step-300);
}

/* Responsive design */
@media (max-width: 640px) {
  .curricular-progress-card .card-body {
    padding: 1rem;
  }
  
  .progress-visualization {
    padding: 1rem;
  }
  
  .progress-stats {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .step-icon {
    width: 1.5rem;
    height: 1.5rem;
  }
  
  .connection-line {
    width: 1rem;
  }
}

@media (max-width: 480px) {
  .progress-visualization .flex {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .progress-stats .stat-item {
    padding: 0.375rem 0.5rem;
  }
  
  .stat-item span {
    font-size: 0.7rem;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .curricular-progress-card,
  .step-icon,
  .connection-line,
  .stat-item,
  .btn-circle {
    transition: none;
  }
  
  .progress-step.completed .step-icon,
  .progress-step.in-progress .step-icon {
    animation: none;
  }
  
  .curricular-progress-card:hover,
  .stat-item:hover,
  .btn-circle:hover {
    transform: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .curricular-progress-card {
    border: 2px solid var(--ion-text-color);
  }

  .progress-visualization {
    border: 1px solid var(--ion-border-color);
  }

  .step-icon {
    border: 2px solid currentColor;
  }

  .connection-line {
    border: 1px solid currentColor;
  }
}
