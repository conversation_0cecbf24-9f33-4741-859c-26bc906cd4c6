import React from 'react';
import {
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardContent,
  IonButton,
  IonIcon
} from '@ionic/react';
import { informationCircleOutline } from 'ionicons/icons';
import './CurricularProgressCard.css';

interface ProgressData {
  subject: string;
  experience: string;
  sequence: string;
  completedPercentage: number;
  inProgressPercentage: number;
}

interface CurricularProgressCardProps {
  data: ProgressData;
  isLoading: boolean;
}

const CurricularProgressCard: React.FC<CurricularProgressCardProps> = ({ data, isLoading }) => {
  if (isLoading) {
    return (
      <IonCard className="curricular-progress-card loading">
        <IonCardContent>
          <div className="loading-skeleton">
            <div className="skeleton-title"></div>
            <div className="skeleton-subtitle"></div>
            <div className="skeleton-progress"></div>
            <div className="skeleton-stats">
              <div className="skeleton-stat"></div>
              <div className="skeleton-stat"></div>
            </div>
          </div>
        </IonCardContent>
      </IonCard>
    );
  }

  return (
    <IonCard className="curricular-progress-card" role="region" aria-label="Tarjeta de progreso curricular">
      <IonCardHeader className="progress-header">
        <div className="header-content">
          <IonCardTitle className="progress-title">
            Progreso curricular
          </IonCardTitle>
          <IonButton
            fill="clear"
            size="small"
            className="info-button"
            aria-label="Información sobre progreso curricular"
          >
            <IonIcon
              icon={informationCircleOutline}
              slot="icon-only"
            />
          </IonButton>
        </div>
      </IonCardHeader>

      <IonCardContent className="progress-content">

        {/* Subject and Experience */}
        <div className="subject-section">
          <div className="subject-header">
            <h4 className="subject-title">
              {data.subject}
            </h4>
            <IonButton
              fill="clear"
              size="small"
              className="details-button"
              aria-label="Ver más detalles"
            >
              <IonIcon
                icon={informationCircleOutline}
                slot="icon-only"
              />
            </IonButton>
          </div>

          <p className="experience-text">
            {data.experience}
          </p>
        </div>

        {/* Progress Sequence */}
        <div className="progress-sequence">
          <div className="sequence-header">
            <span className="sequence-text">
              {data.sequence}
            </span>
            <div className="sequence-divider"></div>
          </div>

          {/* Progress Visualization */}
          <div className="progress-visualization">
            <div className="flex items-center justify-between mb-4">
              {/* Progress Steps */}
              <div className="flex items-center gap-4">
                {/* Completed Step */}
                <div className="progress-step completed">
                  <div className="step-icon bg-success text-success-content">
                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="3">
                      <polyline points="20,6 9,17 4,12"/>
                    </svg>
                  </div>
                </div>

                {/* Connection Line */}
                <div className="connection-line bg-success h-0.5 w-8"></div>

                {/* In Progress Step */}
                <div className="progress-step in-progress">
                  <div className="step-icon bg-warning text-warning-content">
                    <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M12 2L2 7v10c0 5.55 3.84 10 9 10s9-4.45 9-10V7L12 2z"/>
                      <path d="M12 8v8"/>
                      <path d="M8 12h8"/>
                    </svg>
                  </div>
                </div>

                {/* Connection Line */}
                <div className="connection-line bg-base-300 h-0.5 w-8"></div>

                {/* Pending Steps */}
                <div className="progress-step pending">
                  <div className="step-icon bg-base-300 text-base-content/40">
                    <div className="w-2 h-2 rounded-full bg-current"></div>
                  </div>
                </div>

                <div className="connection-line bg-base-300 h-0.5 w-8"></div>

                <div className="progress-step pending">
                  <div className="step-icon bg-base-300 text-base-content/40">
                    <div className="w-2 h-2 rounded-full bg-current"></div>
                  </div>
                </div>
              </div>

              {/* Arrow Navigation */}
              <IonButton
                fill="solid"
                size="small"
                shape="round"
                color="primary"
                className="nav-button"
                aria-label="Siguiente paso"
              >
                <IonIcon
                  icon={informationCircleOutline}
                  slot="icon-only"
                />
              </IonButton>
            </div>

            {/* Progress Statistics */}
            <div className="progress-stats flex gap-4">
              <div className="stat-item flex items-center gap-2">
                <div className="w-4 h-4 bg-success rounded-full"></div>
                <span className="text-xs font-medium text-base-content/70">
                  {data.completedPercentage}%
                </span>
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                  <polyline points="14,2 14,8 20,8"/>
                  <line x1="16" y1="13" x2="8" y2="13"/>
                  <line x1="16" y1="17" x2="8" y2="17"/>
                  <polyline points="10,9 9,9 8,9"/>
                </svg>
              </div>

              <div className="stat-item flex items-center gap-2">
                <div className="w-4 h-4 bg-warning rounded-full"></div>
                <span className="text-xs font-medium text-base-content/70">
                  {data.inProgressPercentage}%
                </span>
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1.5">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                  <polyline points="14,2 14,8 20,8"/>
                  <line x1="16" y1="13" x2="8" y2="13"/>
                  <line x1="16" y1="17" x2="8" y2="17"/>
                  <polyline points="10,9 9,9 8,9"/>
                </svg>
              </div>
            </div>
          </div>
        </div>
      </IonCardContent>
    </IonCard>
  );
};

export default CurricularProgressCard;
