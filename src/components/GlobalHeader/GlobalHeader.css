/* Global Header - Ionic Integration with Catalog Design */

/* Base header styles */
.global-header {
  --background: var(--ion-toolbar-background, var(--ion-background-color));
  --color: var(--ion-toolbar-color, var(--ion-text-color));
  --border-color: var(--ion-toolbar-border-color, var(--ion-border-color));
}

.global-header-toolbar {
  --background: transparent;
  --color: inherit;
  --border-color: inherit;
  --padding-start: 16px;
  --padding-end: 16px;
  --min-height: 56px;
}

/* Standard header styles for non-catalog pages */
.header-title {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 18px;
  color: var(--ion-text-color);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-action-btn {
  --color: var(--ion-text-color);
  --padding-start: 8px;
  --padding-end: 8px;
  --border-radius: 8px;
  transition: all 0.2s ease;
}

.header-action-btn:hover {
  --background: var(--ion-color-step-150);
}

.back-button {
  --color: var(--ion-text-color);
  --padding-start: 8px;
  --padding-end: 8px;
  --border-radius: 8px;
  transition: all 0.2s ease;
}

.back-button:hover {
  --background: var(--ion-color-step-150);
}

/* Catalog-style header */
.global-header.catalog-style {
  --background: var(--ion-color-step-50);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--ion-border-color);
  position: sticky;
  top: 0;
  z-index: 100;
}

.global-header.catalog-style .global-header-toolbar {
  --padding-start: 16px;
  --padding-end: 16px;
  --min-height: auto;
}

.catalog-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
  width: 100%;
  max-width: 100%;
}

.catalog-header-left {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.catalog-back-button {
  --color: var(--ion-text-color);
  --padding-start: 8px;
  --padding-end: 8px;
  --border-radius: 50%;
  --background: transparent;
  transition: all 0.2s ease;
  min-width: 40px;
  min-height: 40px;
}

.catalog-back-button:hover {
  --background: var(--ion-color-step-150);
  transform: translateX(-2px);
}

/* Student dropdown styles */
.student-dropdown-container {
  flex: 1;
  max-width: 280px;
}

.student-dropdown-button {
  --background: var(--ion-card-background);
  --color: var(--ion-text-color);
  --border-radius: 8px;
  --padding-start: 16px;
  --padding-end: 16px;
  --padding-top: 7px;
  --padding-bottom: 7px;
  border: 1px solid var(--ion-border-color);
  transition: all 0.2s ease;
  width: 100%;
  justify-content: space-between;
  text-align: left;
}

.student-dropdown-button:hover {
  box-shadow: 0 4px 12px var(--ion-color-step-200);
  --background: var(--ion-color-step-100);
}

.student-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.student-avatar {
  width: 25px;
  height: 25px;
  flex-shrink: 0;
}

.student-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.student-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
  flex: 1;
}

.student-label {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: 10px;
  line-height: 1.2;
  color: var(--ion-text-color);
  opacity: 0.7;
}

.student-name {
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-size: 12px;
  line-height: 1.2;
  color: var(--ion-text-color);
}

.dropdown-icon {
  font-size: 16px;
  opacity: 0.6;
}

/* Catalog header actions */
.catalog-header-right {
  display: flex;
  align-items: center;
  gap: 0;
}

.catalog-action-btn {
  --color: var(--ion-text-color);
  --background: transparent;
  --padding-start: 0;
  --padding-end: 0;
  --border-radius: 8px;
  transition: all 0.2s ease;
}

.catalog-action-btn:hover {
  --background: var(--ion-color-step-150);
  transform: translateY(-1px);
}

.action-icon-container {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--ion-card-background);
  border-radius: 6px;
  box-shadow: 0 -1px 10px 1px var(--ion-color-step-100);
  transition: all 0.2s ease;
}

.catalog-action-btn:hover .action-icon-container {
  box-shadow: 0 2px 12px var(--ion-color-step-200);
}

/* Popover styles */
.student-dropdown-container ion-popover ion-item.selected {
  --background: var(--ion-color-primary-tint);
  --color: var(--ion-color-primary-contrast);
}

/* Responsive design */
@media (max-width: 640px) {

  
  .catalog-header-content {
    gap: 12px;
  }
  
  .catalog-header-left {
    gap: 6px;
  }
  
  .student-dropdown-button {
    --padding-start: 12px;
    --padding-end: 12px;
    --padding-top: 6px;
    --padding-bottom: 6px;
  }
  
  .student-details {
    gap: 1px;
  }
  
  .student-label {
    font-size: 9px;
  }
  
  .student-name {
    font-size: 11px;
  }
  
  .action-icon-container {
    width: 36px;
    height: 36px;
  }
  
  .action-icon-container ion-icon {
    font-size: 20px;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .catalog-back-button,
  .student-dropdown-button,
  .catalog-action-btn,
  .action-icon-container,
  .header-action-btn,
  .back-button {
    transition: none;
  }
  
  .catalog-back-button:hover,
  .catalog-action-btn:hover {
    transform: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .global-header.catalog-style {
    border-bottom: 2px solid var(--ion-text-color);
  }

  .student-dropdown-button {
    border: 2px solid var(--ion-border-color);
  }

  .action-icon-container {
    border: 1px solid var(--ion-border-color);
  }
}
