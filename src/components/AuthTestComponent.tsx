/**
 * Authentication Test Component
 * Provides UI for testing session isolation functionality
 */

import React, { useState } from 'react';
import { IonButton, IonCard, IonCardContent, IonCardHeader, IonCardTitle, IonItem, IonLabel, IonList, IonText, IonAlert } from '@ionic/react';
import { CapacitorAuthService } from '../services/capacitor-auth.service';
import { AuthSessionIsolationTest } from '../test/auth-session-isolation.test';

interface TestResult {
  test: string;
  status: 'pending' | 'success' | 'error';
  message: string;
  timestamp?: Date;
}

export const AuthTestComponent: React.FC = () => {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [showInstructions, setShowInstructions] = useState(false);

  const addTestResult = (test: string, status: 'success' | 'error', message: string) => {
    setTestResults(prev => [
      ...prev,
      { test, status, message, timestamp: new Date() }
    ]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const runAutomatedTests = async () => {
    setIsRunning(true);
    clearResults();
    
    try {
      addTestResult('Automated Tests', 'pending', 'Starting automated session isolation tests...');
      
      await AuthSessionIsolationTest.runTests();
      
      addTestResult('Automated Tests', 'success', 'All automated tests passed successfully');
    } catch (error) {
      addTestResult('Automated Tests', 'error', `Automated tests failed: ${error}`);
    } finally {
      setIsRunning(false);
    }
  };

  const testSilentSignIn = async () => {
    try {
      addTestResult('Silent Sign-In', 'pending', 'Testing silent sign-in...');
      
      const result = await CapacitorAuthService.silentSignIn();
      
      if (result) {
        addTestResult('Silent Sign-In', 'success', `Found existing session for user: ${result.profile.name || result.profile.email || result.profile.sub}`);
      } else {
        addTestResult('Silent Sign-In', 'success', 'No existing session found (as expected)');
      }
    } catch (error) {
      addTestResult('Silent Sign-In', 'error', `Silent sign-in failed: ${error}`);
    }
  };

  const testFreshSignIn = async () => {
    try {
      addTestResult('Fresh Sign-In', 'pending', 'Starting fresh authentication (will open browser)...');
      
      const result = await CapacitorAuthService.signIn();
      
      addTestResult('Fresh Sign-In', 'success', `Authentication successful for user: ${result.profile.name || result.profile.email || result.profile.sub}`);
    } catch (error) {
      addTestResult('Fresh Sign-In', 'error', `Fresh sign-in failed: ${error}`);
    }
  };

  const testSignOut = async () => {
    try {
      addTestResult('Sign Out', 'pending', 'Signing out and clearing session...');
      
      await CapacitorAuthService.signOut();
      
      addTestResult('Sign Out', 'success', 'Sign out completed successfully');
    } catch (error) {
      addTestResult('Sign Out', 'error', `Sign out failed: ${error}`);
    }
  };

  const debugAuthState = async () => {
    try {
      addTestResult('Debug State', 'pending', 'Checking authentication state...');
      
      await CapacitorAuthService.debugAuthState();
      
      addTestResult('Debug State', 'success', 'Debug information logged to console');
    } catch (error) {
      addTestResult('Debug State', 'error', `Debug state failed: ${error}`);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'success';
      case 'error': return 'danger';
      case 'pending': return 'warning';
      default: return 'medium';
    }
  };

  return (
    <div style={{ padding: '20px' }}>
      <IonCard>
        <IonCardHeader>
          <IonCardTitle>Authentication Session Isolation Tests</IonCardTitle>
        </IonCardHeader>
        <IonCardContent>
          <IonText>
            <p>Use these tests to verify that authentication session isolation is working correctly.</p>
          </IonText>
          
          <IonList>
            <IonItem>
              <IonButton 
                expand="block" 
                onClick={runAutomatedTests} 
                disabled={isRunning}
              >
                {isRunning ? 'Running Tests...' : 'Run Automated Tests'}
              </IonButton>
            </IonItem>
            
            <IonItem>
              <IonButton 
                expand="block" 
                fill="outline" 
                onClick={testSilentSignIn}
              >
                Test Silent Sign-In
              </IonButton>
            </IonItem>
            
            <IonItem>
              <IonButton 
                expand="block" 
                fill="outline" 
                onClick={testFreshSignIn}
              >
                Test Fresh Sign-In (Opens Browser)
              </IonButton>
            </IonItem>
            
            <IonItem>
              <IonButton 
                expand="block" 
                fill="outline" 
                onClick={testSignOut}
              >
                Test Sign Out
              </IonButton>
            </IonItem>
            
            <IonItem>
              <IonButton 
                expand="block" 
                fill="outline" 
                onClick={debugAuthState}
              >
                Debug Auth State
              </IonButton>
            </IonItem>
            
            <IonItem>
              <IonButton 
                expand="block" 
                fill="clear" 
                onClick={() => setShowInstructions(true)}
              >
                Show Manual Test Instructions
              </IonButton>
            </IonItem>
            
            <IonItem>
              <IonButton 
                expand="block" 
                fill="clear" 
                color="medium"
                onClick={clearResults}
              >
                Clear Results
              </IonButton>
            </IonItem>
          </IonList>
        </IonCardContent>
      </IonCard>

      {testResults.length > 0 && (
        <IonCard>
          <IonCardHeader>
            <IonCardTitle>Test Results</IonCardTitle>
          </IonCardHeader>
          <IonCardContent>
            <IonList>
              {testResults.map((result, index) => (
                <IonItem key={index}>
                  <IonLabel>
                    <h3 style={{ color: `var(--ion-color-${getStatusColor(result.status)})` }}>
                      {result.test}
                    </h3>
                    <p>{result.message}</p>
                    {result.timestamp && (
                      <p style={{ fontSize: '0.8em', color: 'var(--ion-color-medium)' }}>
                        {result.timestamp.toLocaleTimeString()}
                      </p>
                    )}
                  </IonLabel>
                </IonItem>
              ))}
            </IonList>
          </IonCardContent>
        </IonCard>
      )}

      <IonAlert
        isOpen={showInstructions}
        onDidDismiss={() => setShowInstructions(false)}
        header="Manual Test Instructions"
        message={`
          <strong>Session Isolation Verification:</strong><br><br>
          
          1. <strong>Initial Test:</strong><br>
          - Tap "Test Fresh Sign-In"<br>
          - Enter VALID credentials<br>
          - Verify authentication succeeds<br><br>
          
          2. <strong>Session Isolation Test:</strong><br>
          - Tap "Test Fresh Sign-In" again<br>
          - Enter INVALID credentials<br>
          - Verify authentication FAILS<br>
          - If it succeeds, session isolation is NOT working<br><br>
          
          3. <strong>Expected Behavior:</strong><br>
          - Each sign-in should require fresh credentials<br>
          - Invalid credentials should always fail<br>
          - No automatic re-authentication
        `}
        buttons={['OK']}
      />
    </div>
  );
};

export default AuthTestComponent;
