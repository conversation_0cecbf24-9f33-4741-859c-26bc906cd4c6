/* Loading <PERSON>ton Component Styles */
.loading-button {
  position: relative;
  transition: all 0.3s ease;
}

.loading-button.loading {
  pointer-events: none;
}

.loading-content,
.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.loading-spinner {
  width: 1rem;
  height: 1rem;
  --color: currentColor;
}

.loading-text {
  font-size: inherit;
  font-weight: inherit;
}

.button-text {
  font-size: inherit;
  font-weight: inherit;
}

.button-icon {
  font-size: 1.1em;
}

.button-icon-only {
  font-size: 1.2em;
}

/* Size variants */
.loading-button[size="small"] .loading-spinner {
  width: 0.8rem;
  height: 0.8rem;
}

.loading-button[size="large"] .loading-spinner {
  width: 1.2rem;
  height: 1.2rem;
}

/* Accessibility improvements */
.loading-button:focus {
  outline: 2px solid var(--ion-color-primary);
  outline-offset: 2px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .loading-button {
    transition: none;
  }
  
  .loading-spinner {
    animation: none;
  }
}
