import React from 'react';
import { <PERSON><PERSON><PERSON>on, Ion<PERSON><PERSON>ner, IonIcon } from '@ionic/react';
import './LoadingButton.css';

interface LoadingButtonProps {
  loading?: boolean;
  disabled?: boolean;
  expand?: 'block' | 'full';
  fill?: 'clear' | 'outline' | 'solid';
  size?: 'small' | 'default' | 'large';
  color?: string;
  type?: 'button' | 'submit' | 'reset';
  className?: string;
  onClick?: () => void;
  children: React.ReactNode;
  loadingText?: string;
  icon?: string;
  iconSlot?: 'start' | 'end' | 'icon-only';
  'aria-label'?: string;
}

const LoadingButton: React.FC<LoadingButtonProps> = ({
  loading = false,
  disabled = false,
  expand,
  fill = 'solid',
  size = 'default',
  color,
  type = 'button',
  className = '',
  onClick,
  children,
  loadingText,
  icon,
  iconSlot = 'start',
  'aria-label': ariaLabel,
  ...props
}) => {
  const isDisabled = disabled || loading;
  const buttonClass = `loading-button ${className} ${loading ? 'loading' : ''}`;

  const renderContent = () => {
    if (loading) {
      return (
        <div className="loading-content">
          <IonSpinner name="crescent" className="loading-spinner" />
          {loadingText && <span className="loading-text">{loadingText}</span>}
        </div>
      );
    }

    return (
      <div className="button-content">
        {icon && iconSlot === 'start' && (
          <IonIcon icon={icon} slot="start" className="button-icon" />
        )}
        <span className="button-text">{children}</span>
        {icon && iconSlot === 'end' && (
          <IonIcon icon={icon} slot="end" className="button-icon" />
        )}
        {icon && iconSlot === 'icon-only' && (
          <IonIcon icon={icon} className="button-icon-only" />
        )}
      </div>
    );
  };

  return (
    <IonButton
      expand={expand}
      fill={fill}
      size={size}
      color={color}
      type={type}
      disabled={isDisabled}
      className={buttonClass}
      onClick={onClick}
      aria-label={ariaLabel}
      {...props}
    >
      {renderContent()}
    </IonButton>
  );
};

export default LoadingButton;
