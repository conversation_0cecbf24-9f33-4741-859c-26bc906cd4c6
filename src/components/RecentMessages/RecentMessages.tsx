import React from 'react';

interface Message {
  id: string;
  teacher: {
    name: string;
    subject: string;
    avatar?: string;
  };
  content: string;
  timestamp: string;
}

interface RecentMessagesProps {
  messages: Message[];
}

const RecentMessages: React.FC<RecentMessagesProps> = ({ messages }) => {
  return (
    <div className="bg-white rounded-xl p-5 shadow-lg mb-6">
      <h3 className="text-lg font-semibold text-gray-700 mb-4"><PERSON><PERSON>jes <PERSON></h3>
      <div className="flex flex-col gap-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className="flex gap-4 p-3 bg-base-200 rounded-lg hover:-translate-y-0.5 transition-transform duration-200 cursor-pointer"
          >
            <div className="w-12 h-12 rounded-full bg-primary flex items-center justify-center text-white font-semibold flex-shrink-0">
              {message.teacher.avatar || message.teacher.name.charAt(0)}
            </div>
            <div className="flex-1">
              <div className="flex justify-between items-start mb-1">
                <div>
                  <h4 className="text-base font-semibold text-gray-700">{message.teacher.name}</h4>
                  <p className="text-sm text-gray-600">{message.teacher.subject}</p>
                </div>
                <span className="text-xs text-gray-400">{message.timestamp}</span>
              </div>
              <p className="text-sm text-gray-600 line-clamp-2">{message.content}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default RecentMessages; 