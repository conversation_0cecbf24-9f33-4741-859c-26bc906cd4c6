import React, { useState, useEffect } from 'react';
import {
  IonButton,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonItem,
  IonLabel,
  IonToggle,
  IonText,
  IonSpinner,
  IonIcon,
  IonAlert,
  IonList,
  IonNote
} from '@ionic/react';
import { checkmarkCircle, alertCircle, settings, refresh } from 'ionicons/icons';
import { CapacitorAuthService } from '../services/capacitor-auth.service';

interface AuthTestResult {
  success: boolean;
  message: string;
  timestamp: Date;
  duration: number;
  mode: 'seamless' | 'system';
}

export const SeamlessAuthTestComponent: React.FC = () => {
  const [isSeamlessEnabled, setIsSeamlessEnabled] = useState(true);
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const [testResults, setTestResults] = useState<AuthTestResult[]>([]);
  const [showA<PERSON><PERSON>, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');

  useEffect(() => {
    // Initialize seamless auth mode
    CapacitorAuthService.setSeamlessAuthMode(isSeamlessEnabled);
  }, [isSeamlessEnabled]);

  const handleSeamlessToggle = (enabled: boolean) => {
    setIsSeamlessEnabled(enabled);
    CapacitorAuthService.setSeamlessAuthMode(enabled);
  };

  const testAuthentication = async () => {
    if (isAuthenticating) return;

    setIsAuthenticating(true);
    const startTime = Date.now();
    const mode = isSeamlessEnabled ? 'seamless' : 'system';

    try {
      console.log(`🧪 [TEST] Starting ${mode} authentication test`);
      
      const result = await CapacitorAuthService.signIn();
      const duration = Date.now() - startTime;

      const testResult: AuthTestResult = {
        success: true,
        message: `Authentication successful! User: ${result.profile.name || result.profile.email}`,
        timestamp: new Date(),
        duration,
        mode
      };

      setTestResults(prev => [testResult, ...prev.slice(0, 4)]); // Keep last 5 results
      setAlertMessage(`✅ Authentication successful!\n\nUser: ${result.profile.name || result.profile.email}\nMode: ${mode}\nDuration: ${duration}ms`);
      setShowAlert(true);

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      const testResult: AuthTestResult = {
        success: false,
        message: `Authentication failed: ${errorMessage}`,
        timestamp: new Date(),
        duration,
        mode
      };

      setTestResults(prev => [testResult, ...prev.slice(0, 4)]);
      setAlertMessage(`❌ Authentication failed!\n\nError: ${errorMessage}\nMode: ${mode}\nDuration: ${duration}ms`);
      setShowAlert(true);

    } finally {
      setIsAuthenticating(false);
    }
  };

  const testSilentAuthentication = async () => {
    try {
      console.log('🔍 [TEST] Testing silent authentication');
      const result = await CapacitorAuthService.silentSignIn();
      
      if (result) {
        setAlertMessage(`✅ Silent authentication successful!\n\nUser: ${result.profile.name || result.profile.email}`);
      } else {
        setAlertMessage('ℹ️ No existing authentication session found');
      }
      setShowAlert(true);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setAlertMessage(`❌ Silent authentication failed!\n\nError: ${errorMessage}`);
      setShowAlert(true);
    }
  };

  const clearTestResults = () => {
    setTestResults([]);
  };

  const formatDuration = (ms: number): string => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  return (
    <IonCard>
      <IonCardHeader>
        <IonCardTitle>
          <IonIcon icon={settings} style={{ marginRight: '8px' }} />
          Seamless Authentication Test
        </IonCardTitle>
      </IonCardHeader>
      
      <IonCardContent>
        {/* Authentication Mode Toggle */}
        <IonItem>
          <IonLabel>
            <h3>Authentication Mode</h3>
            <p>{isSeamlessEnabled ? 'Seamless WebView (Hidden)' : 'System Browser (Visible)'}</p>
          </IonLabel>
          <IonToggle
            checked={isSeamlessEnabled}
            onIonChange={(e) => handleSeamlessToggle(e.detail.checked)}
            disabled={isAuthenticating}
          />
        </IonItem>

        {/* Test Buttons */}
        <div style={{ padding: '16px 0' }}>
          <IonButton
            expand="block"
            onClick={testAuthentication}
            disabled={isAuthenticating}
            color="primary"
          >
            {isAuthenticating ? (
              <>
                <IonSpinner name="crescent" style={{ marginRight: '8px' }} />
                Authenticating...
              </>
            ) : (
              `Test ${isSeamlessEnabled ? 'Seamless' : 'System'} Authentication`
            )}
          </IonButton>

          <IonButton
            expand="block"
            fill="outline"
            onClick={testSilentAuthentication}
            disabled={isAuthenticating}
            style={{ marginTop: '8px' }}
          >
            Test Silent Authentication
          </IonButton>
        </div>

        {/* Test Results */}
        {testResults.length > 0 && (
          <>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
              <IonText>
                <h3>Recent Test Results</h3>
              </IonText>
              <IonButton
                fill="clear"
                size="small"
                onClick={clearTestResults}
              >
                <IonIcon icon={refresh} />
                Clear
              </IonButton>
            </div>

            <IonList>
              {testResults.map((result, index) => (
                <IonItem key={index}>
                  <IonIcon
                    icon={result.success ? checkmarkCircle : alertCircle}
                    color={result.success ? 'success' : 'danger'}
                    style={{ marginRight: '12px' }}
                  />
                  <IonLabel>
                    <h3>{result.success ? 'Success' : 'Failed'}</h3>
                    <p>{result.message}</p>
                    <IonNote>
                      {result.mode} mode • {formatDuration(result.duration)} • {result.timestamp.toLocaleTimeString()}
                    </IonNote>
                  </IonLabel>
                </IonItem>
              ))}
            </IonList>
          </>
        )}

        {/* Information */}
        <IonItem lines="none" style={{ marginTop: '16px' }}>
          <IonLabel>
            <IonText color="medium">
              <p>
                <strong>Seamless Mode:</strong> Uses hidden WebView for native-feeling authentication.
                <br />
                <strong>System Mode:</strong> Uses visible system browser (fallback).
                <br />
                <strong>Security:</strong> Each test performs complete session isolation.
              </p>
            </IonText>
          </IonLabel>
        </IonItem>
      </IonCardContent>

      {/* Alert Dialog */}
      <IonAlert
        isOpen={showAlert}
        onDidDismiss={() => setShowAlert(false)}
        header="Authentication Test Result"
        message={alertMessage}
        buttons={['OK']}
      />
    </IonCard>
  );
};
