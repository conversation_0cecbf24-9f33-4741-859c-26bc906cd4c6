.catalogo-header {
  background: var(--ion-color-step-50);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--ion-border-color);
  padding: 60px 16px 8px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
  max-width: 100%;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.back-button {
  color: var(--ion-text-color);
  transition: all 0.2s ease;
}

.back-button:hover {
  background: var(--ion-color-step-150);
  transform: translateX(-2px);
}

.student-dropdown {
  flex: 1;
  max-width: 280px;
  border: 1px solid var(--ion-border-color);
  transition: all 0.2s ease;
}

.student-dropdown:hover {
  box-shadow: 0 4px 12px var(--ion-color-step-200);
}

.dropdown-button {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 7px 16px;
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dropdown-button:hover {
  background: var(--ion-color-step-100);
}

.student-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.student-avatar {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.student-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
}

.student-label {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: 10px;
  line-height: 1.2;
  color: var(--ion-text-color);
}

.student-name {
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-size: 12px;
  line-height: 1.2;
  color: var(--ion-text-color);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 0;
}

.header-action-btn {
  color: var(--ion-text-color);
  transition: all 0.2s ease;
}

.header-action-btn:hover {
  background: var(--ion-color-step-150);
  transform: translateY(-1px);
}

.action-icon-container {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--ion-card-background);
  border-radius: 6px;
  box-shadow: 0 -1px 10px 1px var(--ion-color-step-100);
  transition: all 0.2s ease;
}

.header-action-btn:hover .action-icon-container {
  box-shadow: 0 2px 12px var(--ion-color-step-200);
}

/* Responsive design */
@media (max-width: 640px) {
  .catalogo-header {
    padding: 52px 12px 6px;
  }
  
  .header-content {
    gap: 12px;
  }
  
  .header-left {
    gap: 6px;
  }
  
  .student-dropdown {
    max-width: 200px;
  }
  
  .dropdown-button {
    padding: 6px 12px;
  }
  
  .student-details {
    gap: 1px;
  }
  
  .student-label {
    font-size: 9px;
  }
  
  .student-name {
    font-size: 11px;
  }
  
  .action-icon-container {
    width: 36px;
    height: 36px;
  }
  
  .action-icon-container svg {
    width: 20px;
    height: 20px;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .back-button,
  .student-dropdown,
  .dropdown-button,
  .header-action-btn,
  .action-icon-container {
    transition: none;
  }
  
  .back-button:hover,
  .header-action-btn:hover {
    transform: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .catalogo-header {
    border-bottom: 2px solid var(--ion-text-color);
  }

  .student-dropdown {
    border: 2px solid var(--ion-border-color);
  }

  .action-icon-container {
    border: 1px solid var(--ion-border-color);
  }
}
