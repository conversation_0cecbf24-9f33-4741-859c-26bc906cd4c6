/* Product Card - Native Ionic Styling */
.product-card {
  --background: var(--ion-card-background);
  --color: var(--ion-card-color);
  border-radius: 24px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.product-image-container {
  position: relative;
  width: 100%;
  height: 188px;
  overflow: hidden;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image {
  transform: scale(1.05);
}

.product-badges {
  position: absolute;
  top: 16px;
  left: 16px;
  right: 16px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.kit-badge {
  background: var(--ion-card-background);
  box-shadow: 0px 4px 6px 0px var(--ion-color-step-200);
  border-radius: 8px;
  padding: 8px 8px 6px;
  display: flex;
  align-items: center;
  gap: 4px;
  height: 32px;
}

.kit-icon {
  width: 12px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.kit-count {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 10px;
  line-height: 1.21;
  color: var(--ion-color-secondary);
}

.product-content {
  --background: transparent;
  --color: inherit;
  padding: 16px 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.product-info {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 24px;
}

.product-title-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.product-name {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 14px;
  line-height: 1.21;
  color: var(--ion-text-color);
  margin: 0;
}

.product-description {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 1.21;
  color: var(--ion-color-medium);
  margin: 0;
}

.kit-type-badge {
  border-radius: 8px;
  padding: 8px;
  display: flex;
  align-items: center;
  gap: 6px;
  height: 32px;
  flex-shrink: 0;
}

.kit-badge-customizable {
  background: var(--ion-color-secondary);
  background-opacity: 0.1;
  background: color-mix(in srgb, var(--ion-color-secondary) 10%, transparent);
}

.kit-badge-complete {
  background: var(--ion-color-warning);
  background-opacity: 0.1;
  background: color-mix(in srgb, var(--ion-color-warning) 10%, transparent);
}

.edit-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.kit-badge-customizable .edit-icon {
  color: var(--ion-color-secondary);
}

.kit-badge-complete .edit-icon {
  color: var(--ion-color-warning);
}

.kit-type-text {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 10px;
  line-height: 1.21;
}

.kit-badge-customizable .kit-type-text {
  color: var(--ion-color-secondary);
}

.kit-badge-complete .kit-type-text {
  color: var(--ion-color-warning);
}

.share-button {
  --color: var(--ion-text-color);
  --padding-start: 0;
  --padding-end: 0;
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.21;
  text-align: left;
  justify-content: flex-start;
  height: auto;
  min-height: auto;
}

.share-button:hover {
  --background: transparent;
  --color: var(--ion-color-primary);
}

.product-divider {
  width: 100%;
  height: 1px;
  background: var(--ion-border-color);
  border-radius: 1px;
}

.product-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.price-section {
  flex: 1;
}

.product-price {
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-size: 21px;
  line-height: 1.21;
  color: var(--ion-text-color);
}

.add-to-cart-btn {
  --background: var(--ion-color-primary);
  --color: var(--ion-color-primary-contrast);
  --border-radius: 90px;
  --padding-start: 16px;
  --padding-end: 16px;
  --padding-top: 12px;
  --padding-bottom: 12px;
  width: 98px;
  height: auto;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.add-to-cart-btn:hover {
  --background: var(--ion-color-primary-shade);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--ion-color-primary-rgb), 0.3);
}

.cart-icon {
  width: 20px;
  height: 20px;
  color: white;
}

/* Responsive design */
@media (max-width: 640px) {
  .product-content {
    padding: 12px 16px;
    gap: 12px;
  }
  
  .product-header {
    gap: 16px;
    flex-direction: column;
    align-items: flex-start;
  }
  
  .product-title-section {
    gap: 6px;
  }
  
  .product-name {
    font-size: 13px;
  }
  
  .product-description {
    font-size: 11px;
  }
  
  .kit-type-badge {
    align-self: flex-start;
    padding: 6px;
    height: 28px;
  }
  
  .kit-type-text {
    font-size: 9px;
  }
  
  .product-price {
    font-size: 18px;
  }
  
  .add-to-cart-btn {
    width: 80px;
    padding: 10px 12px;
  }
  
  .cart-icon {
    width: 18px;
    height: 18px;
  }
}

@media (max-width: 480px) {
  .product-image-container {
    height: 160px;
  }
  
  .product-badges {
    top: 12px;
    left: 12px;
    right: 12px;
  }
  
  .kit-badge {
    padding: 6px 6px 4px;
    height: 28px;
  }
  
  .kit-count {
    font-size: 9px;
  }
  
  .product-content {
    padding: 10px 12px;
    gap: 10px;
  }
  
  .product-footer {
    gap: 12px;
  }
  
  .product-price {
    font-size: 16px;
  }
  
  .add-to-cart-btn {
    width: 70px;
    padding: 8px 10px;
  }
  
  .cart-icon {
    width: 16px;
    height: 16px;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .product-card,
  .product-image,
  .add-to-cart-btn {
    transition: none;
  }
  
  .product-card:hover,
  .add-to-cart-btn:hover {
    transform: none;
  }
  
  .product-card:hover .product-image {
    transform: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .product-card {
    border: 2px solid var(--ion-text-color);
  }

  .kit-badge,
  .kit-type-badge {
    border: 1px solid currentColor;
  }

  .product-divider {
    background: var(--ion-text-color);
    height: 2px;
  }
}
