import React from 'react';
import {
  IonCard,
  IonCardContent,
  IonButton,
  IonIcon
} from '@ionic/react';
import { cartOutline, shareOutline } from 'ionicons/icons';
import './ProductCard.css';

interface ProductData {
  id: string;
  name: string;
  description: string;
  price: number;
  installments: string;
  image: string;
  kitType: 'customizable' | 'complete';
  itemCount: number;
  publisher: string;
}

interface ProductCardProps {
  product: ProductData;
  onAddToCart: () => void;
  onShare: () => void;
}

const ProductCard: React.FC<ProductCardProps> = ({ product, onAddToCart, onShare }) => {
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(price);
  };

  const getKitTypeColor = (kitType: 'customizable' | 'complete') => {
    return kitType === 'customizable' 
      ? 'kit-badge-customizable' 
      : 'kit-badge-complete';
  };

  const getKitTypeText = (kitType: 'customizable' | 'complete') => {
    return kitType === 'customizable' 
      ? 'Kit customizable' 
      : 'Kit completo';
  };

  return (
    <IonCard className="product-card" role="article" aria-label={`Producto ${product.name}`}>
      <div className="product-image-container">
        <img
          src={product.image}
          alt={product.name}
          className="product-image"
          loading="lazy"
        />
        <div className="product-badges">
          <div className="kit-badge">
            <div className="kit-icon">
              <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                <path d="M6.04 11.07C6.04 11.07 5.04 10.07 3.5 10.07C2 10.07 1 11.07 1 11.07V13.07C1 13.07 2 14.07 3.5 14.07C5 14.07 6.04 13.07 6.04 13.07V11.07Z" fill="#35A192"/>
                <path d="M0.42 10.33L5.04 0L10 9" stroke="#35A192" strokeWidth="1"/>
              </svg>
            </div>
            <span className="kit-count">{product.itemCount} items</span>
          </div>
        </div>
      </div>

      <IonCardContent className="product-content">
        <div className="product-info">
          <div className="product-header">
            <div className="product-title-section">
              <h4 className="product-name">{product.name}</h4>
              <p className="product-description">{product.installments}</p>
            </div>
            <div className={`kit-type-badge ${getKitTypeColor(product.kitType)}`}>
              <div className="edit-icon">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M0 12V16H4L12.67 7.33L8.67 3.33L0 12Z" stroke="currentColor" strokeWidth="1"/>
                  <path d="M14.67 5.33C15.07 4.93 15.07 4.27 14.67 3.87L12.13 1.33C11.73 0.93 11.07 0.93 10.67 1.33L8.67 3.33L12.67 7.33L14.67 5.33Z" stroke="currentColor" strokeWidth="1"/>
                  <path d="M10.67 5.33L12.67 7.33" stroke="currentColor" strokeWidth="1"/>
                </svg>
              </div>
              <span className="kit-type-text">{getKitTypeText(product.kitType)}</span>
            </div>
          </div>
        </div>

        <IonButton
          fill="clear"
          size="small"
          className="share-button"
          onClick={onShare}
          aria-label={`Compartir ${product.name}`}
        >
          <IonIcon icon={shareOutline} slot="start" />
          {product.publisher}
        </IonButton>

        <div className="product-divider"></div>

        <div className="product-footer">
          <div className="price-section">
            <span className="product-price">{formatPrice(product.price)}</span>
          </div>
          <IonButton
            fill="solid"
            color="primary"
            className="add-to-cart-btn"
            onClick={onAddToCart}
            aria-label={`Agregar ${product.name} al carrito`}
          >
            <IonIcon icon={cartOutline} slot="start" />
            Agregar
          </IonButton>
        </div>
      </IonCardContent>
    </IonCard>
  );
};

export default ProductCard;
