import './ExploreContainer.css';

interface ContainerProps {
  name: string;
}

const ExploreContainer: React.FC<ContainerProps> = ({ name }) => {
  return (
    <div className="container">
      <strong >{name}</strong>
      <p >Explore<a target="_blank" rel="noopener noreferrer" href="https://ionicframework.com/docs/components">UI Components</a></p>
      <div className='text-xl text-red-500 font-bold'>TEXTO DE PRUEBA</div>
    </div>
  );
};

export default ExploreContainer;
