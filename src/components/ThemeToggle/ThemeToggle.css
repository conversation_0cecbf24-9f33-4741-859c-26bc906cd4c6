/* Native Ionic Theme Toggle Component - Redesigned */
.theme-toggle-item {
  --background: transparent;
  --color: inherit;
  --padding-start: 0;
  --padding-end: 0;
  --inner-padding-start: 0;
  --inner-padding-end: 0;
  --border-style: none;
  margin-bottom: 0.5rem;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.theme-toggle-item:hover {
  --background: var(--ion-color-light);
  transform: translateX(4px);
}

.ion-palette-dark .theme-toggle-item:hover {
  --background: var(--ion-color-dark);
}

/* Main Icon */
.theme-main-icon {
  color: var(--ion-color-primary);
  font-size: 1.25rem;
  margin-right: 1rem;
  transition: all 0.3s ease;
}

.theme-toggle-item:hover .theme-main-icon {
  color: var(--ion-color-primary-shade);
  transform: scale(1.1);
}

/* Label Content */
.theme-label {
  --color: inherit;
  flex: 1;
  min-width: 0;
}

.theme-title {
  color: var(--ion-text-color);
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
  line-height: 1.2;
}

.theme-description {
  color: var(--ion-color-medium);
  font-size: 0.875rem;
  margin: 0;
  line-height: 1.3;
  transition: color 0.3s ease;
}

/* Toggle Control */
.theme-toggle-control {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.theme-state-indicators {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.375rem 0.75rem;
  background: var(--ion-color-light);
  border-radius: 50px;
  transition: all 0.3s ease;
  border: 1px solid var(--ion-color-light-shade);
}

.ion-palette-dark .theme-state-indicators {
  background: var(--ion-color-dark);
  border-color: var(--ion-color-dark-shade);
}

.theme-state-icon {
  font-size: 1rem;
  color: var(--ion-color-medium);
  transition: all 0.3s ease;
  opacity: 0.6;
}

.theme-state-icon.active {
  opacity: 1;
  transform: scale(1.1);
}

.theme-state-icon.light.active {
  color: #ff9800;
}

.theme-state-icon.dark.active {
  color: var(--ion-color-primary);
}

.ion-palette-dark .theme-state-icon.light.active {
  color: #ffb74d;
}

.ion-palette-dark .theme-state-icon.dark.active {
  color: var(--ion-color-primary-tint);
}

/* Native Ionic Toggle */
.theme-toggle-switch {
  --background: var(--ion-color-medium-tint);
  --background-checked: var(--ion-color-primary);
  --handle-background: white;
  --handle-background-checked: white;
  --handle-box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  --handle-width: 20px;
  --handle-height: 20px;
  --track-width: 36px;
  --track-height: 22px;
  margin: 0;
  transition: all 0.3s ease;
}

.ion-palette-dark .theme-toggle-switch {
  --background: var(--ion-color-medium);
  --background-checked: var(--ion-color-primary);
  --handle-background: var(--ion-color-light);
  --handle-background-checked: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .theme-main-icon {
    font-size: 1.125rem;
  }

  .theme-title {
    font-size: 0.9375rem;
  }

  .theme-description {
    font-size: 0.8125rem;
  }

  .theme-state-indicators {
    gap: 0.375rem;
    padding: 0.3125rem 0.625rem;
  }

  .theme-state-icon {
    font-size: 0.9375rem;
  }
}

@media (max-width: 480px) {
  .theme-toggle-item {
    --inner-padding-start: 0.75rem;
    --inner-padding-end: 0.75rem;
  }

  .theme-main-icon {
    font-size: 1rem;
    margin-right: 0.75rem;
  }

  .theme-title {
    font-size: 0.875rem;
  }

  .theme-description {
    font-size: 0.75rem;
  }

  .theme-state-indicators {
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
  }

  .theme-state-icon {
    font-size: 0.875rem;
  }

  .theme-toggle-switch {
    --handle-width: 18px;
    --handle-height: 18px;
    --track-width: 32px;
    --track-height: 20px;
  }
}

/* Accessibility & Focus Styles */
.theme-toggle-switch:focus {
  outline: 2px solid var(--ion-color-primary);
  outline-offset: 2px;
  border-radius: 50px;
}

.theme-toggle-item:focus-within {
  --background: var(--ion-color-light-tint);
  outline: 2px solid var(--ion-color-primary);
  outline-offset: 2px;
}

.ion-palette-dark .theme-toggle-item:focus-within {
  --background: var(--ion-color-dark-tint);
}

/* Touch Target Optimization */
.theme-toggle-item {
  min-height: 48px;
  padding: 0.75rem 0;
}

.theme-state-indicators {
  min-height: 32px;
  min-width: 80px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .theme-toggle-item {
    border: 1px solid var(--ion-color-medium);
  }

  .theme-toggle-item:hover,
  .theme-toggle-item:focus-within {
    border-color: var(--ion-color-primary);
    border-width: 2px;
  }

  .theme-state-indicators {
    border: 2px solid var(--ion-color-medium);
  }

  .theme-toggle-switch {
    --handle-box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .theme-toggle-item,
  .theme-main-icon,
  .theme-state-indicators,
  .theme-state-icon,
  .theme-toggle-switch,
  .theme-description {
    transition: none !important;
  }

  .theme-toggle-item:hover {
    transform: none;
  }

  .theme-toggle-item:hover .theme-main-icon,
  .theme-state-icon.active {
    transform: none;
  }
}

/* Smooth Theme Transition Animation */
@keyframes themeTransition {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.theme-main-icon {
  animation: themeTransition 0.3s ease-in-out;
}

@media (prefers-reduced-motion: reduce) {
  .theme-main-icon {
    animation: none;
  }
}

/* Integration with Account Page Settings */
.settings-list .theme-toggle-item {
  margin-bottom: 0.5rem;
}

.settings-card .theme-toggle-item {
  border-radius: 12px;
}

/* Dark Mode Enhancements */
.ion-palette-dark .theme-state-indicators:hover {
  background: var(--ion-color-dark-tint);
  border-color: var(--ion-color-dark);
}

.ion-palette-dark .theme-toggle-item:hover .theme-main-icon {
  color: var(--ion-color-primary-tint);
}
