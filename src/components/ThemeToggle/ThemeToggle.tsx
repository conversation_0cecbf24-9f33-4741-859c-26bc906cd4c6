import React from 'react';
import {
  IonItem,
  IonLabel,
  IonToggle,
  IonIcon,
  ToggleCustomEvent,
} from '@ionic/react';
import { moonOutline, sunnyOutline, contrastOutline } from 'ionicons/icons';
import { useTheme } from '../../contexts/ThemeContext';
import './ThemeToggle.css';

interface ThemeToggleProps {
  className?: string;
}

/**
 * Native Ionic Theme Toggle Component
 * Redesigned for seamless integration with Account page and modern Ionic patterns
 * Maintains all accessibility features and responsive design
 */
const ThemeToggle: React.FC<ThemeToggleProps> = ({ className = '' }) => {
  const { isDarkMode, toggleTheme } = useTheme();

  const handleToggleChange = (event: ToggleCustomEvent) => {
    toggleTheme();
  };

  return (
    <IonItem
      className={`theme-toggle-item ${className}`}
      lines="none"
      button={false}
    >
      {/* Main Icon */}
      <IonIcon
        icon={contrastOutline}
        slot="start"
        className="theme-main-icon"
        aria-hidden="true"
      />

      {/* Label Content */}
      <IonLabel className="theme-label">
        <h3 className="theme-title">Tema de la aplicación</h3>
        <p className="theme-description">
          {isDarkMode ? 'Modo oscuro activado' : 'Modo claro activado'}
        </p>
      </IonLabel>

      {/* Toggle Control with State Icons */}
      <div className="theme-toggle-control" slot="end">
        <div className="theme-state-indicators">
          <IonIcon
            icon={sunnyOutline}
            className={`theme-state-icon light ${!isDarkMode ? 'active' : ''}`}
            aria-hidden="true"
          />
          <IonToggle
            checked={isDarkMode}
            onIonChange={handleToggleChange}
            className="theme-toggle-switch"
            aria-label="Alternar entre modo claro y oscuro"
            color="primary"
          />
          <IonIcon
            icon={moonOutline}
            className={`theme-state-icon dark ${isDarkMode ? 'active' : ''}`}
            aria-hidden="true"
          />
        </div>
      </div>
    </IonItem>
  );
};

export default ThemeToggle;
