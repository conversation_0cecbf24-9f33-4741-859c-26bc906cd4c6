import React from 'react';
import { Ion<PERSON>ard, IonIcon } from '@ionic/react';
import { chevronForward, arrowForwardOutline, checkmarkCircleOutline, documentTextOutline } from 'ionicons/icons';

interface Sequence {
  id: string;
  name: string;
  progress: number;
  isCompleted: boolean;
}

interface Subject {
  id: string;
  name: string;
  currentExperience: string;
  sequences: Sequence[];
}

interface CurriculumProgressProps {
  subjects: Subject[];
}

const CurriculumProgress: React.FC<CurriculumProgressProps> = ({ subjects }) => {
  return (
    <div className="flex flex-col gap-6">
      {subjects.map((subject) => (
        <IonCard key={subject.id} className="bg-[#F9F9F9] p-3 m-0">
          <div className="flex flex-col gap-3">
            {/* Subject Header */}
            <div className="flex items-center justify-between">
              <h4 className="text-[14px] font-semibold text-[#4E4E4E]">{subject.name}</h4>
              <button className="w-4 h-4 text-primary">
                <IonIcon icon={chevronForward} />
              </button>
            </div>

            {/* Current Experience */}
            <p className="text-[10px] text-[#4E4E4E] leading-tight">
              {subject.currentExperience}
            </p>

            {/* Sequence Progress */}
            <div className="flex flex-col gap-0.5">
              {/* Sequence Title */}
              <div className="flex flex-col items-center gap-0.5">
                <span className="text-[10px] text-[#4E4E4E]">Secuencia N°2</span>
                <div className="w-px h-2 border-l border-dashed border-primary/50"></div>
              </div>

              {/* Progress Line */}
              <div className="relative">
                <div className="absolute left-0 right-0 top-3 h-px bg-[#D9D9D9]"></div>
                <div className="absolute left-0 w-[40%] top-3 h-0.5 bg-primary/50"></div>

                {/* Progress Points */}
                <div className="flex justify-between relative">
                  {/* Current Point */}
                  <div className="flex items-center gap-11">
                    <div className="relative">
                      <div className="w-6 h-6 rounded-xl bg-white shadow-sm flex items-center justify-center">
                        <div className="w-4 h-4">
                          <IonIcon icon={arrowForwardOutline} />
                        </div>
                      </div>
                      <div className="absolute -right-1.5 -top-1.5 w-3.5 h-3.5 rounded-full bg-primary"></div>
                    </div>
                  </div>

                  {/* Completed Point */}
                  <div className="flex items-center gap-11">
                    <div className="w-6 h-6 rounded-xl bg-white shadow-sm flex items-center justify-center">
                      <div className="w-4 h-4">
                        <IonIcon icon={checkmarkCircleOutline} />
                      </div>
                    </div>
                  </div>

                  {/* Future Points */}
                  {[1, 2, 3].map((_, index) => (
                    <div key={index} className="w-2 h-2 rounded-full bg-[#D9D9D9] mt-2"></div>
                  ))}
                </div>
              </div>

              {/* Progress Details */}
              <div className="flex justify-between mt-2">
                <div className="flex flex-col items-center gap-0.5">
                  <span className="text-[8px] text-[#4E4E4E]">60%</span>
                  <div className="w-[17px] h-4">
                    <IonIcon icon={documentTextOutline} />
                  </div>
                </div>
                <div className="flex flex-col items-center gap-0.5">
                  <span className="text-[8px] text-[#4E4E4E]">30%</span>
                  <div className="w-[17px] h-4">
                    <IonIcon icon={documentTextOutline} />
                  </div>
                </div>
                {[1, 2, 3].map((_, index) => (
                  <div key={index} className="flex flex-col items-center gap-0.5">
                    <span className="text-[8px] text-[#D9D9D9]">-</span>
                    <div className="w-[17px] h-4 text-[#D9D9D9]">
                      <IonIcon icon={documentTextOutline} />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </IonCard>
      ))}
    </div>
  );
};

export default CurriculumProgress; 