/* Welcome Card - Native Ionic Styling */
.welcome-card {
  --background: var(--ion-card-background);
  --color: var(--ion-card-color);
  margin-bottom: 1.5rem;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.welcome-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.welcome-card-header {
  --background: transparent;
  --color: inherit;
  padding: 0;
}

.welcome-header-section {
  position: relative;
  background: linear-gradient(135deg,
    color-mix(in srgb, var(--ion-color-primary) 10%, transparent) 0%,
    color-mix(in srgb, var(--ion-color-secondary) 10%, transparent) 100%);
  border-radius: 16px 16px 0 0;
  padding: 1.5rem;
  overflow: hidden;
}

.welcome-content-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.welcome-icon-container {
  flex-shrink: 0;
  width: 3rem;
  height: 3rem;
  background: color-mix(in srgb, var(--ion-color-primary) 20%, transparent);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: gentle-bounce 3s ease-in-out infinite;
}

.welcome-icon {
  font-size: 1.5rem;
  color: var(--ion-color-primary);
}

.welcome-content {
  flex: 1;
}

@keyframes gentle-bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-3px);
  }
}

.welcome-title {
  --color: var(--ion-text-color);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-weight: 600;
  font-size: 1.25rem;
  line-height: 1.3;
  margin-bottom: 0.5rem;
}

.welcome-description {
  color: var(--ion-color-medium);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-weight: 400;
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0;
}

.welcome-card-footer {
  height: 0.75rem;
  background: color-mix(in srgb, var(--ion-color-secondary) 5%, transparent);
  border-radius: 0 0 16px 16px;
}

/* Responsive design */
@media (max-width: 640px) {
  .welcome-header-section {
    padding: 1rem;
  }

  .welcome-content-wrapper {
    gap: 0.75rem;
  }

  .welcome-icon-container {
    width: 2.5rem;
    height: 2.5rem;
  }

  .welcome-icon {
    font-size: 1.25rem;
  }

  .welcome-title {
    font-size: 1.125rem;
  }

  .welcome-description {
    font-size: 0.8125rem;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .welcome-card {
    transition: none;
  }
  
  .welcome-card:hover {
    transform: none;
  }
  
  .welcome-icon-container {
    animation: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .welcome-card {
    border: 2px solid var(--ion-text-color);
  }

  .welcome-title,
  .welcome-description {
    color: var(--ion-text-color);
  }
}
