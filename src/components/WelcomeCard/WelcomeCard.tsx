import React from 'react';
import {
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardContent,
  IonIcon
} from '@ionic/react';
import { peopleOutline } from 'ionicons/icons';
import './WelcomeCard.css';

const WelcomeCard: React.FC = () => {
  return (
    <IonCard className="welcome-card" role="banner" aria-label="Tarjeta de bienvenida">
      <IonCardHeader className="welcome-card-header">
        <div className="welcome-header-section">
          <div className="welcome-content-wrapper">
            <div className="welcome-icon-container" aria-hidden="true">
              <IonIcon
                icon={peopleOutline}
                className="welcome-icon"
              />
            </div>

            <div className="welcome-content">
              <IonCardTitle className="welcome-title">
                Hola, bienvenido.
              </IonCardTitle>
              <p className="welcome-description">
                En este lugar encontrarás los avances más significativos del estudiante durante su periodo académico.
              </p>
            </div>
          </div>
        </div>
      </IonCardHeader>
    </IonCard>
  );
};

export default WelcomeCard;