import React from 'react';
import { IonList, IonItem, IonIcon, IonLabel, IonMenuToggle } from '@ionic/react';
import { home, analytics, link, book, person } from 'ionicons/icons';

interface MenuItem {
  id: string;
  label: string;
  icon: string;
  path: string;
}

interface NavigationProps {
  items: MenuItem[];
  activeItem: string;
  onItemClick: (item: MenuItem) => void;
}

const getIonIcon = (iconName: string) => {
  switch (iconName) {
    case '🏠': return home;
    case '📊': return analytics;
    case '🔗': return link;
    case '📚': return book;
    case '👤': return person;
    default: return home;
  }
};

export const Navigation: React.FC<NavigationProps> = ({
  items,
  activeItem,
  onItemClick,
}) => {
  return (
    <IonList>
      {items.map((item) => (
        <IonMenuToggle key={item.id} autoHide={false}>
          <IonItem
            button
            detail={false}
            color={activeItem === item.id ? 'primary' : undefined}
            onClick={() => onItemClick(item)}
          >
            <IonIcon
              slot="start"
              icon={getIonIcon(item.icon)}
              color={activeItem === item.id ? 'light' : 'medium'}
            />
            <IonLabel>{item.label}</IonLabel>
          </IonItem>
        </IonMenuToggle>
      ))}
    </IonList>
  );
};

export default Navigation; 