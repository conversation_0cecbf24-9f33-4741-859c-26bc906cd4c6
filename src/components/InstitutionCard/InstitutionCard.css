.institution-card {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 32px;
}

.institution-header {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  gap: 32px;
  width: 100%;
}

.institution-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.institution-name {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 14px;
  line-height: 1.21;
  color: var(--ion-text-color);
  margin: 0;
}

.institution-details {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 10px;
  line-height: 1.21;
  color: var(--ion-color-medium);
  margin: 0;
}

.year-badge {
  background: var(--ion-color-primary);
  border-radius: 8px;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 48px;
  height: 32px;
  flex-shrink: 0;
}

.year-text {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 10px;
  line-height: 1.21;
  color: var(--ion-color-primary-contrast);
}

.products-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
}

/* Responsive design */
@media (max-width: 640px) {
  .institution-card {
    gap: 8px;
    margin-bottom: 24px;
  }
  
  .institution-header {
    gap: 16px;
    flex-direction: column;
    align-items: flex-start;
  }
  
  .institution-info {
    gap: 4px;
  }
  
  .institution-name {
    font-size: 13px;
  }
  
  .institution-details {
    font-size: 9px;
  }
  
  .year-badge {
    align-self: flex-start;
    padding: 6px 8px;
    min-width: 40px;
    height: 28px;
  }
  
  .year-text {
    font-size: 9px;
  }
  
  .products-section {
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .institution-header {
    gap: 12px;
  }
  
  .institution-name {
    font-size: 12px;
    line-height: 1.3;
  }
  
  .institution-details {
    font-size: 8px;
    line-height: 1.3;
  }
  
  .year-badge {
    padding: 4px 6px;
    min-width: 36px;
    height: 24px;
  }
  
  .year-text {
    font-size: 8px;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .institution-card * {
    transition: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .institution-name,
  .institution-details {
    color: var(--ion-text-color);
  }

  .year-badge {
    border: 2px solid var(--ion-color-primary);
  }
}
