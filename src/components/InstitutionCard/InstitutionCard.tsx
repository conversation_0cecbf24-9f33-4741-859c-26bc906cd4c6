import React from 'react';
import ProductCard from '../ProductCard/ProductCard';
import './InstitutionCard.css';

interface ProductData {
  id: string;
  name: string;
  description: string;
  price: number;
  installments: string;
  image: string;
  kitType: 'customizable' | 'complete';
  itemCount: number;
  publisher: string;
}

interface InstitutionData {
  id: string;
  name: string;
  details: string;
  year: string;
  products: ProductData[];
}

interface InstitutionCardProps {
  institution: InstitutionData;
  onAddToCart: (productId: string) => void;
  onShare: (productId: string) => void;
}

const InstitutionCard: React.FC<InstitutionCardProps> = ({ 
  institution, 
  onAddToCart, 
  onShare 
}) => {
  return (
    <div className="institution-card" role="region" aria-label={`Institución ${institution.name}`}>
      <div className="institution-header">
        <div className="institution-info">
          <h3 className="institution-name">{institution.name}</h3>
          <p className="institution-details">{institution.details}</p>
        </div>
        <div className="year-badge">
          <span className="year-text">{institution.year}</span>
        </div>
      </div>

      <div className="products-section">
        {institution.products.map((product) => (
          <ProductCard
            key={product.id}
            product={product}
            onAddToCart={() => onAddToCart(product.id)}
            onShare={() => onShare(product.id)}
          />
        ))}
      </div>
    </div>
  );
};

export default InstitutionCard;
