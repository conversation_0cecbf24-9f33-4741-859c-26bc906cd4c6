/**
 * Error boundary specifically for authentication-related errors
 * Prevents app crashes and provides recovery options
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { IonPage, IonContent, IonButton, IonCard, IonCardContent, IonIcon, IonText } from '@ionic/react';
import { alertCircleOutline, refreshOutline, homeOutline } from 'ionicons/icons';
import { debugLog } from '../../config/environment.config';
import { AuthErrorHandler } from '../../utils/auth-error-handler';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
}

export class AuthErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorId: `auth-error-${Date.now()}`
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log the error
    console.error('AuthErrorBoundary - Caught error:', error, errorInfo);
    debugLog('AuthErrorBoundary - Error details:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack
    });

    this.setState({
      error,
      errorInfo,
      errorId: `auth-error-${Date.now()}`
    });

    // Parse the error for better handling
    const authError = AuthErrorHandler.parseError(error);
    debugLog('AuthErrorBoundary - Parsed error:', authError);

    // Report to error tracking service if available
    this.reportError(error, errorInfo, authError);
  }

  private reportError(error: Error, errorInfo: ErrorInfo, authError: any) {
    // This would integrate with your error reporting service
    const errorReport = {
      errorId: this.state.errorId,
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      authError: authError,
      userAgent: navigator.userAgent,
      url: window.location.href,
      timestamp: new Date().toISOString()
    };

    debugLog('AuthErrorBoundary - Error report:', errorReport);
    
    // In a real app, you would send this to your error tracking service
    // Example: Sentry.captureException(error, { extra: errorReport });
  }

  private handleRetry = () => {
    // Clear error state and try again
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    });
  };

  private handleGoHome = () => {
    // Navigate to home page
    window.location.href = '/';
  };

  private handleReload = () => {
    // Reload the entire app
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <IonPage>
          <IonContent className="ion-padding">
            <div className="flex flex-col items-center justify-center h-full text-center">
              <IonCard className="w-full max-w-md">
                <IonCardContent>
                  <div className="text-red-500 mb-4">
                    <IonIcon icon={alertCircleOutline} className="text-6xl" />
                  </div>
                  
                  <h2 className="text-xl font-semibold mb-2">Error de Autenticación</h2>
                  
                  <IonText color="medium" className="block mb-4">
                    Se produjo un error durante el proceso de autenticación. 
                    Puedes intentar las siguientes opciones:
                  </IonText>

                  <div className="space-y-3">
                    <IonButton 
                      expand="block" 
                      fill="solid" 
                      color="primary"
                      onClick={this.handleRetry}
                    >
                      <IonIcon icon={refreshOutline} slot="start" />
                      Intentar de Nuevo
                    </IonButton>

                    <IonButton 
                      expand="block" 
                      fill="outline" 
                      color="primary"
                      onClick={this.handleGoHome}
                    >
                      <IonIcon icon={homeOutline} slot="start" />
                      Ir al Inicio
                    </IonButton>

                    <IonButton 
                      expand="block" 
                      fill="clear" 
                      color="medium"
                      onClick={this.handleReload}
                    >
                      <IonIcon icon={refreshOutline} slot="start" />
                      Recargar Aplicación
                    </IonButton>
                  </div>

                  {/* Debug information (only in development) */}
                  {process.env.NODE_ENV === 'development' && this.state.error && (
                    <details className="mt-4 text-left">
                      <summary className="cursor-pointer text-sm text-gray-500">
                        Información de Debug
                      </summary>
                      <div className="mt-2 p-2 bg-gray-100 rounded text-xs font-mono">
                        <div className="mb-2">
                          <strong>Error ID:</strong> {this.state.errorId}
                        </div>
                        <div className="mb-2">
                          <strong>Message:</strong> {this.state.error.message}
                        </div>
                        {this.state.error.stack && (
                          <div>
                            <strong>Stack:</strong>
                            <pre className="whitespace-pre-wrap text-xs">
                              {this.state.error.stack}
                            </pre>
                          </div>
                        )}
                      </div>
                    </details>
                  )}
                </IonCardContent>
              </IonCard>
            </div>
          </IonContent>
        </IonPage>
      );
    }

    return this.props.children;
  }
}

export default AuthErrorBoundary;
