import React from 'react';
import {
  IonContent,
  IonPage,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonItem,
  IonLabel,
  IonInput,
  IonButton,
  IonIcon,
  IonList,
  IonItemDivider,
} from '@ionic/react';
import {
  checkmarkCircleOutline,
  alertCircleOutline,
  eyeOutline,
  mailOutline,
  lockClosedOutline,
} from 'ionicons/icons';
import { useTheme } from '../../contexts/ThemeContext';
import ThemeToggle from '../ThemeToggle/ThemeToggle';
import './DarkModeTest.css';

/**
 * Test component to verify dark mode styling fixes
 * This component demonstrates all the fixed styling issues
 */
const DarkModeTest: React.FC = () => {
  const { isDarkMode } = useTheme();

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar>
          <IonTitle>Dark Mode Test - {isDarkMode ? 'Dark' : 'Light'} Mode</IonTitle>
        </IonToolbar>
      </IonHeader>
      
      <IonContent>
        <div className="test-container">
          {/* Theme Toggle Test */}
          <IonCard>
            <IonCardHeader>
              <IonCardTitle>
                <IonIcon icon={checkmarkCircleOutline} color="success" />
                Theme Toggle Test
              </IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              <p>Test the theme toggle component styling:</p>
              <ThemeToggle />
              <p className="test-note">
                ✅ Toggle should show proper colors and state indication
              </p>
            </IonCardContent>
          </IonCard>

          {/* Auth Form Test */}
          <IonCard className="auth-test-card">
            <IonCardHeader>
              <IonCardTitle>
                <IonIcon icon={checkmarkCircleOutline} color="success" />
                Auth Form Test
              </IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              <p>Test auth form styling in current theme:</p>
              
              <div className="form-test">
                <IonItem className="form-item" lines="none">
                  <IonIcon icon={mailOutline} slot="start" className="form-icon" />
                  <IonLabel position="stacked">Email</IonLabel>
                  <IonInput
                    type="email"
                    placeholder="<EMAIL>"
                    className="form-input"
                  />
                </IonItem>

                <IonItem className="form-item" lines="none">
                  <IonIcon icon={lockClosedOutline} slot="start" className="form-icon" />
                  <IonLabel position="stacked">Password</IonLabel>
                  <IonInput
                    type="password"
                    placeholder="Enter password"
                    className="form-input"
                  />
                  <IonButton fill="clear" slot="end" className="password-toggle">
                    <IonIcon icon={eyeOutline} />
                  </IonButton>
                </IonItem>

                <div className="error-text">
                  <IonIcon icon={alertCircleOutline} className="error-icon" />
                  <small>This is an error message test</small>
                </div>

                <IonButton expand="block" className="auth-submit-button">
                  Test Submit Button
                </IonButton>
              </div>

              <p className="test-note">
                ✅ Form should have proper background and text contrast
              </p>
            </IonCardContent>
          </IonCard>

          {/* Color Test */}
          <IonCard>
            <IonCardHeader>
              <IonCardTitle>
                <IonIcon icon={checkmarkCircleOutline} color="success" />
                Color Contrast Test
              </IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              <IonList>
                <IonItemDivider>Text Colors</IonItemDivider>
                <IonItem>
                  <IonLabel>
                    <h2>Primary Text (--ion-text-color)</h2>
                    <p>Secondary text should be readable</p>
                  </IonLabel>
                </IonItem>
                
                <IonItemDivider>Background Colors</IonItemDivider>
                <IonItem>
                  <IonLabel>
                    <h2>Card Background</h2>
                    <p>Should contrast properly with text</p>
                  </IonLabel>
                </IonItem>
              </IonList>
              
              <p className="test-note">
                ✅ All text should be clearly readable with proper contrast
              </p>
            </IonCardContent>
          </IonCard>

          {/* Status Summary */}
          <IonCard className="status-card">
            <IonCardHeader>
              <IonCardTitle>
                <IonIcon icon={checkmarkCircleOutline} color="success" />
                Fix Status Summary
              </IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              <div className="status-list">
                <div className="status-item">
                  <IonIcon icon={checkmarkCircleOutline} color="success" />
                  <span>Auth card background fixed</span>
                </div>
                <div className="status-item">
                  <IonIcon icon={checkmarkCircleOutline} color="success" />
                  <span>Form input colors corrected</span>
                </div>
                <div className="status-item">
                  <IonIcon icon={checkmarkCircleOutline} color="success" />
                  <span>Theme toggle colors improved</span>
                </div>
                <div className="status-item">
                  <IonIcon icon={checkmarkCircleOutline} color="success" />
                  <span>Text contrast enhanced</span>
                </div>
                <div className="status-item">
                  <IonIcon icon={checkmarkCircleOutline} color="success" />
                  <span>Error messages visibility fixed</span>
                </div>
              </div>
            </IonCardContent>
          </IonCard>
        </div>
      </IonContent>
    </IonPage>
  );
};

export default DarkModeTest;
