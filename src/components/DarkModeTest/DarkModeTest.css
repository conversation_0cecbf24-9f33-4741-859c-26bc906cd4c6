/* Dark Mode Test Component Styles */
.test-container {
  padding: 1rem;
  max-width: 600px;
  margin: 0 auto;
}

.auth-test-card {
  margin: 1rem 0;
}

.form-test {
  margin: 1rem 0;
}

.form-test .form-item {
  margin-bottom: 1rem;
}

.test-note {
  margin-top: 1rem;
  padding: 0.75rem;
  background: var(--ion-color-light, #f8f9fa);
  border-radius: 8px;
  font-size: 0.9rem;
  color: var(--ion-color-step-600, #666);
  border-left: 4px solid var(--ion-color-success);
}

.ion-palette-dark .test-note {
  background: var(--ion-color-step-100, #2a2a2a);
  color: var(--ion-color-step-700, #b8b8b8);
}

.status-card {
  margin-top: 2rem;
}

.status-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: var(--ion-color-light, #f8f9fa);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.ion-palette-dark .status-item {
  background: var(--ion-color-step-100, #2a2a2a);
}

.status-item span {
  color: var(--ion-text-color);
  font-weight: 500;
}

.status-item ion-icon {
  flex-shrink: 0;
}
