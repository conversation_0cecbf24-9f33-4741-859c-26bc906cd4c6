import React from 'react';
import {
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardContent,
  IonButton,
  IonIcon
} from '@ionic/react';
import { informationCircleOutline } from 'ionicons/icons';
import './AttendanceCard.css';

interface AttendanceData {
  date: string;
  dayName: string;
  dayNumber: number;
  amStatus: 'present' | 'late' | 'justified' | 'unjustified';
  pmStatus: 'present' | 'late' | 'justified' | 'unjustified';
}

interface AttendanceCardProps {
  data: AttendanceData[];
  isLoading: boolean;
  month: string;
  period: string;
}

const getStatusColor = (status: 'present' | 'late' | 'justified' | 'unjustified'): string => {
  switch (status) {
    case 'present': return 'bg-success';
    case 'late': return 'bg-warning';
    case 'justified': return 'bg-info';
    case 'unjustified': return 'bg-error';
    default: return 'bg-base-300';
  }
};

const getStatusLabel = (status: 'present' | 'late' | 'justified' | 'unjustified'): string => {
  switch (status) {
    case 'present': return 'Asistencia';
    case 'late': return 'Retrasos';
    case 'justified': return 'Justificadas';
    case 'unjustified': return 'Injustificadas';
    default: return '';
  }
};

const AttendanceCard: React.FC<AttendanceCardProps> = ({ data, isLoading, month, period }) => {
  if (isLoading) {
    return (
      <IonCard className="attendance-card loading">
        <IonCardContent>
          <div className="loading-skeleton">
            <div className="skeleton-title"></div>
            <div className="skeleton-subtitle"></div>
            <div className="skeleton-grid">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="skeleton-day"></div>
              ))}
            </div>
          </div>
        </IonCardContent>
      </IonCard>
    );
  }

  // Calculate attendance statistics
  const stats = data.reduce((acc, day) => {
    [day.amStatus, day.pmStatus].forEach(status => {
      acc[status] = (acc[status] || 0) + 1;
    });
    return acc;
  }, {} as Record<string, number>);

  return (
    <IonCard className="attendance-card" role="region" aria-label="Tarjeta de asistencia del alumno">
      <IonCardHeader className="attendance-header">
        <div className="header-content">
          <IonCardTitle className="attendance-title">
            Asistencia del alumno
          </IonCardTitle>
          <IonButton
            fill="clear"
            size="small"
            className="info-button"
            aria-label="Información sobre asistencia"
          >
            <IonIcon
              icon={informationCircleOutline}
              slot="icon-only"
            />
          </IonButton>
        </div>

        <div className="period-info">
          <div className="period-badge">
            {period}
          </div>
          <span className="month-text">
            {month}
          </span>
        </div>
      </IonCardHeader>

      <IonCardContent className="attendance-content">
        {/* Attendance Grid */}
        <div className="attendance-grid">
          <div className="days-grid">
            {data.map((day, index) => (
              <div key={index} className="attendance-day">
                <div className="day-name">
                  {day.dayName}
                </div>
                <div className="day-number">
                  {day.dayNumber}
                </div>

                {/* AM Status */}
                <div className="status-section">
                  <div className="status-label">A.M</div>
                  <div
                    className={`status-indicator ${getStatusColor(day.amStatus)}`}
                    role="img"
                    aria-label={`Mañana: ${getStatusLabel(day.amStatus)}`}
                  ></div>
                </div>

                {/* PM Status */}
                <div className="status-section">
                  <div className="status-label">P.M</div>
                  <div
                    className={`status-indicator ${getStatusColor(day.pmStatus)}`}
                    role="img"
                    aria-label={`Tarde: ${getStatusLabel(day.pmStatus)}`}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Legend */}
        <div className="attendance-legend">
          <div className="legend-items">
            {Object.entries(stats).map(([status, count]) => (
              <div key={status} className="legend-item">
                <div className={`legend-indicator ${getStatusColor(status as any)}`}></div>
                <span className="legend-text">
                  {getStatusLabel(status as any)} ({count})
                </span>
              </div>
            ))}
          </div>
        </div>
      </IonCardContent>
    </IonCard>
  );
};

export default AttendanceCard;
