/* Attendance Card - Native Ionic Styling */
.attendance-card {
  --background: var(--ion-card-background);
  --color: var(--ion-card-color);
  margin-bottom: 1.5rem;
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.attendance-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.attendance-header {
  --background: transparent;
  --color: inherit;
  padding-bottom: 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.attendance-title {
  --color: var(--ion-text-color);
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
}

.info-button {
  --color: var(--ion-color-medium);
  --padding-start: 8px;
  --padding-end: 8px;
  --border-radius: 50%;
  width: 32px;
  height: 32px;
}

.period-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.period-badge {
  background: var(--ion-color-light);
  color: var(--ion-color-dark);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid var(--ion-color-light-shade);
}

.month-text {
  color: var(--ion-color-medium);
  font-size: 0.875rem;
  font-weight: 500;
}

.attendance-content {
  --background: transparent;
  --color: inherit;
  padding-top: 0;
}

.attendance-grid {
  position: relative;
  margin-bottom: 1.5rem;
}

.days-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 1rem;
}

.attendance-day {
  text-align: center;
  padding: 0.75rem 0.5rem;
  border-radius: 12px;
  background: var(--ion-color-light);
  transition: all 0.2s ease;
  position: relative;
  border: 1px solid var(--ion-color-light-shade);
}

.attendance-day:hover {
  background: var(--ion-color-light-tint);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.day-name {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--ion-color-medium);
  margin-bottom: 0.5rem;
}

.day-number {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--ion-color-medium);
  margin-bottom: 0.75rem;
}

.status-section {
  margin-bottom: 0.5rem;
}

.status-section:last-child {
  margin-bottom: 0;
}

.status-label {
  font-size: 0.625rem;
  color: var(--ion-color-medium-shade);
  margin-bottom: 0.25rem;
}

.status-indicator {
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  margin: 0 auto;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.attendance-day:hover .status-indicator {
  transform: scale(1.1);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

/* Legend styling */
.attendance-legend {
  padding-top: 1rem;
  border-top: 1px solid var(--ion-color-light-shade);
}

.legend-items {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
}

.legend-indicator {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.legend-text {
  color: var(--ion-color-medium);
}

/* Status colors using Ionic color system */
.bg-success {
  background-color: var(--ion-color-success);
  border: 1px solid var(--ion-color-success-shade);
}

.bg-warning {
  background-color: var(--ion-color-warning);
  border: 1px solid var(--ion-color-warning-shade);
}

.bg-info {
  background-color: var(--ion-color-tertiary);
  border: 1px solid var(--ion-color-tertiary-shade);
}

.bg-error {
  background-color: var(--ion-color-danger);
  border: 1px solid var(--ion-color-danger-shade);
}

/* Loading skeleton styles */
.loading-skeleton {
  padding: 1rem;
}

.skeleton-title {
  height: 1.5rem;
  background: var(--ion-color-light);
  border-radius: 4px;
  margin-bottom: 1rem;
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.skeleton-subtitle {
  height: 1rem;
  background: var(--ion-color-light);
  border-radius: 4px;
  margin-bottom: 1.5rem;
  width: 75%;
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.skeleton-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 1rem;
}

.skeleton-day {
  height: 5rem;
  background: var(--ion-color-light);
  border-radius: 12px;
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Responsive design */
@media (max-width: 640px) {
  .days-grid {
    gap: 0.75rem;
  }

  .attendance-day {
    padding: 0.5rem 0.25rem;
  }

  .legend-items {
    gap: 0.5rem;
  }

  .legend-item {
    font-size: 0.6875rem;
  }
}

@media (max-width: 480px) {
  .days-grid {
    gap: 0.5rem;
  }

  .status-indicator {
    width: 0.75rem;
    height: 0.75rem;
  }

  .legend-indicator {
    width: 0.625rem;
    height: 0.625rem;
  }

  .skeleton-grid {
    gap: 0.5rem;
  }
}

/* Dark mode support */
.ion-palette-dark .period-badge {
  background: var(--ion-color-dark);
  color: var(--ion-color-light);
  border-color: var(--ion-color-dark-shade);
}

.ion-palette-dark .attendance-day {
  background: var(--ion-color-dark);
  border-color: var(--ion-color-dark-shade);
}

.ion-palette-dark .attendance-day:hover {
  background: var(--ion-color-dark-tint);
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .attendance-card,
  .attendance-day,
  .status-indicator {
    transition: none;
  }

  .attendance-card:hover,
  .attendance-day:hover,
  .attendance-day:hover .status-indicator {
    transform: none;
  }

  .skeleton-title,
  .skeleton-subtitle,
  .skeleton-day {
    animation: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .attendance-card {
    border: 2px solid var(--ion-text-color);
  }

  .attendance-day {
    border: 2px solid var(--ion-color-medium);
  }

  .bg-success,
  .bg-warning,
  .bg-info,
  .bg-error {
    border-width: 2px;
  }
}

/* Loading animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
