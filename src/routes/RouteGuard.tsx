import React, { useEffect } from 'react';
import { Redirect, useLocation } from 'react-router-dom';
import { useAuth } from 'react-oidc-context';
import { useUser } from '../contexts/UserContext';
import { debugLog } from '../config/user-manager.config';
import { ROUTES } from './routes';
import { Capacitor } from '@capacitor/core';

const RouteGuard: React.FC<React.PropsWithChildren> = ({ children }) => {
  const location = useLocation();
  const auth = useAuth();
  const { isAuthenticated, isLoading, user } = useUser();
  const hasSeenWelcome = localStorage.getItem('hasSeenWelcome') === 'true';

  useEffect(() => {
    debugLog('RouteGuard - Route change', {
      pathname: location.pathname,
      hasSeenWelcome,
      isOIDCAuthenticated: auth.isAuthenticated,
      isUserContextAuthenticated: isAuthenticated,
      isLoading,
      hasUser: !!user,
      userEmail: user?.email,
      isNativePlatform: Capacitor.isNativePlatform(),
      platform: Capacitor.getPlatform()
    });
  }, [location.pathname, hasSeenWelcome, isAuthenticated, isLoading, user]);

  // Show loading while OIDC authentication is initializing
  if (isLoading) {
    debugLog('RouteGuard - Loading state, waiting for OIDC authentication');
    return null; // or a loading spinner
  }

  // Si el usuario no ha visto la bienvenida, mostrar los slides
  if (!hasSeenWelcome && location.pathname !== ROUTES.WELCOME) {
    debugLog('RouteGuard - User has not seen welcome, redirecting to welcome');
    return <Redirect to={ROUTES.WELCOME} />;
  }

  // Si el usuario ha visto la bienvenida pero no está autenticado
  if (hasSeenWelcome && !isAuthenticated && location.pathname !== ROUTES.AUTH) {
    debugLog('RouteGuard - User not authenticated, redirecting to auth', {
      isOIDCAuth: auth.isAuthenticated,
      isUserContextAuth: isAuthenticated,
      hasUser: !!user,
      isNative: Capacitor.isNativePlatform()
    });
    return <Redirect to={ROUTES.AUTH} />;
  }

  // Usuario autenticado - permitir acceso
  debugLog('RouteGuard - User authenticated, allowing access', {
    userName: user?.name,
    userEmail: user?.email,
    authMethod: Capacitor.isNativePlatform() ? 'Capacitor' : 'OIDC'
  });

  return <>{children}</>;
};

export default RouteGuard;
