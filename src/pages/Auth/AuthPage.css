/* Enhanced Auth Page Styles */
.auth-content {
  --background: var(--ion-background-color);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  position: relative;
}



/* Main Container */
.auth-container {
  width: 100%;
  max-width: 420px;
  margin: 0 auto;
}

/* Auth Card */
.auth-card {
  --background: var(--ion-card-background, #ffffff);
  border-radius: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  background: var(--ion-card-background, #ffffff);
}

.ion-palette-dark .auth-card {
  --background: var(--ion-card-background, #1e1e1e);
  background: var(--ion-card-background, #1e1e1e);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.auth-card-content {
  padding: 2rem;
  background: transparent;
  color: var(--ion-text-color);
}

/* Header */
.auth-header {
  text-align: center;
  margin-bottom: 2rem;
}

.auth-icon-container {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--ion-color-primary), var(--ion-color-secondary));
  border-radius: 50%;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 16px rgba(var(--ion-color-primary-rgb), 0.3);
}

.auth-main-icon {
  font-size: 2rem;
  color: white;
}

.auth-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--ion-text-color);
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
}

.auth-subtitle {
  font-size: 0.95rem;
  color: var(--ion-color-medium);
  margin: 0;
  line-height: 1.4;
}

/* Form Styles */
.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-item {
  --background: var(--ion-color-light, #f8f9fa);
  --border-radius: 12px;
  --padding-start: 1rem;
  --padding-end: 1rem;
  --min-height: 56px;
  --color: var(--ion-text-color);
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.ion-palette-dark .form-item {
  --background: var(--ion-color-step-100, #2a2a2a);
  --color: var(--ion-text-color);
}

.form-item:focus-within {
  --background: var(--ion-color-light-tint, #ffffff);
  --color: var(--ion-text-color);
  border-color: var(--ion-color-primary);
  box-shadow: 0 0 0 3px rgba(var(--ion-color-primary-rgb), 0.1);
}

.ion-palette-dark .form-item:focus-within {
  --background: var(--ion-color-step-150, #363636);
  --color: var(--ion-text-color);
}

.form-item-error {
  border-color: var(--ion-color-danger) !important;
  --background: rgba(var(--ion-color-danger-rgb), 0.05);
}

.form-icon {
  font-size: 1.2rem;
  color: var(--ion-color-medium);
  margin-right: 0.5rem;
  transition: color 0.3s ease;
}

.form-item:focus-within .form-icon {
  color: var(--ion-color-primary);
}

.form-item-error .form-icon {
  color: var(--ion-color-danger);
}

.form-input {
  --padding-start: 0;
  --padding-top: 1rem;
  --padding-bottom: 0.5rem;
  --color: var(--ion-text-color);
  font-size: 1rem;
}

.form-item ion-label {
  font-size: 0.85rem;
  font-weight: 600;
  color: var(--ion-color-medium);
  margin-bottom: 0.25rem;
  transition: color 0.3s ease;
}

.ion-palette-dark .form-item ion-label {
  color: var(--ion-color-step-700, #b8b8b8);
}

.form-item:focus-within ion-label {
  color: var(--ion-color-primary);
}

.form-item-error ion-label {
  color: var(--ion-color-danger);
}

/* Password Toggle */
.password-toggle {
  --color: var(--ion-color-medium);
  --padding-start: 0.5rem;
  --padding-end: 0.5rem;
  margin: 0;
}

.password-toggle:hover {
  --color: var(--ion-color-primary);
}

/* Error Messages */
.error-text {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin-top: 0.25rem;
  padding-left: 0.5rem;
  color: var(--ion-color-danger);
}

.error-icon {
  font-size: 0.9rem;
  flex-shrink: 0;
  color: var(--ion-color-danger);
}

.ion-palette-dark .error-text {
  color: var(--ion-color-danger-tint);
}

.ion-palette-dark .error-icon {
  color: var(--ion-color-danger-tint);
}

/* Submit Button */
.auth-submit-button {
  --background: linear-gradient(135deg, var(--ion-color-primary), var(--ion-color-secondary));
  --background-hover: linear-gradient(135deg, var(--ion-color-primary-shade), var(--ion-color-secondary-shade));
  --color: white;
  --border-radius: 12px;
  --box-shadow: 0 4px 16px rgba(var(--ion-color-primary-rgb), 0.3);
  --padding-top: 1rem;
  --padding-bottom: 1rem;
  font-size: 1rem;
  font-weight: 600;
  margin-top: 1rem;
  transition: all 0.3s ease;
  text-transform: none;
}

.auth-submit-button:hover {
  --box-shadow: 0 6px 20px rgba(var(--ion-color-primary-rgb), 0.4);
  transform: translateY(-2px);
}

.auth-submit-button:disabled {
  --background: var(--ion-color-medium);
  --color: var(--ion-color-medium-contrast);
  --box-shadow: none;
  transform: none;
}

.loading-spinner {
  margin-right: 0.5rem;
  width: 1rem;
  height: 1rem;
}

.loading-text {
  margin-left: 0.5rem;
}

/* Footer */
.auth-footer {
  margin-top: 2rem;
  text-align: center;
}

.toggle-mode-button {
  --color: var(--ion-color-medium);
  --color-hover: var(--ion-color-primary);
  font-size: 0.9rem;
  font-weight: 500;
  text-transform: none;
  transition: all 0.3s ease;
}

.toggle-mode-button:hover {
  --color: var(--ion-color-primary);
}

/* Alert Styles */
.auth-alert.alert-success {
  --color: var(--ion-color-success);
}

.auth-alert.alert-error {
  --color: var(--ion-color-danger);
}

/* Responsive Design */
@media (max-width: 480px) {
  .auth-content {
    padding: 0.5rem;
  }

  .auth-card-content {
    padding: 1.5rem;
  }

  .auth-icon-container {
    width: 60px;
    height: 60px;
    margin-bottom: 1rem;
  }

  .auth-main-icon {
    font-size: 1.5rem;
  }

  .auth-title {
    font-size: 1.5rem;
  }


}

@media (max-width: 360px) {
  .auth-card-content {
    padding: 1rem;
  }

  .form-item {
    --min-height: 48px;
  }
}

/* Smooth Transitions for Theme Changes */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Focus Styles for Accessibility */
.auth-submit-button:focus,
.toggle-mode-button:focus,
.password-toggle:focus,
.theme-toggle-switch:focus {
  outline: 2px solid var(--ion-color-primary);
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .form-item {
    border: 2px solid var(--ion-color-medium);
  }

  .form-item:focus-within {
    border-color: var(--ion-color-primary);
    border-width: 3px;
  }

  .auth-card {
    border: 1px solid var(--ion-color-medium);
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
    animation: none !important;
  }

  .auth-submit-button:hover {
    transform: none;
  }
}

.toggle-mode-button {
  margin-top: 1rem;
  font-size: 0.9rem;
}