/* Account Page - Modern Mobile Design */
.account-content {
  --background: var(--ion-background-color);
  --padding-start: 1rem;
  --padding-end: 1rem;
  --padding-top: 1rem;
  --padding-bottom: 2rem;
}

/* Profile Header Card */
.profile-header-card {
  --background: var(--ion-card-background);
  --color: var(--ion-card-color);
  margin-bottom: 1rem;
  border-radius: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.profile-header-content {
  --background: transparent;
  --color: inherit;
  padding: 1.5rem;
}

.profile-main {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  position: relative;
}

.profile-avatar {
  width: 80px;
  height: 80px;
  flex-shrink: 0;
  border: 3px solid var(--ion-color-primary-tint);
  box-shadow: 0 4px 12px rgba(var(--ion-color-primary-rgb), 0.2);
}

.profile-info {
  flex: 1;
  min-width: 0;
}

.profile-name {
  color: var(--ion-text-color);
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
}

.role-chip {
  --background: color-mix(in srgb, var(--ion-color-primary) 15%, transparent);
  --color: var(--ion-color-primary);
  margin: 0 0 0.75rem 0;
  font-size: 0.75rem;
  font-weight: 600;
  height: 28px;
}

.member-since {
  color: var(--ion-color-medium);
  font-size: 0.875rem;
  margin: 0;
  font-weight: 500;
}

.edit-profile-btn {
  --color: var(--ion-color-medium);
  --padding-start: 8px;
  --padding-end: 8px;
  --border-radius: 50%;
  width: 40px;
  height: 40px;
  position: absolute;
  top: 0;
  right: 0;
}

.edit-profile-btn:hover {
  --color: var(--ion-color-primary);
  --background: color-mix(in srgb, var(--ion-color-primary) 10%, transparent);
}

/* Children Overview Card */
.children-card {
  --background: var(--ion-card-background);
  --color: var(--ion-card-color);
  margin-bottom: 1rem;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
}

.children-content {
  --background: transparent;
  --color: inherit;
  padding-top: 0;
}

.child-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background: var(--ion-color-light);
  border-radius: 12px;
  margin-bottom: 0.75rem;
  transition: all 0.2s ease;
}

.child-item:last-child {
  margin-bottom: 0;
}

.child-item:hover {
  background: var(--ion-color-light-tint);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.child-info {
  flex: 1;
}

.child-name {
  color: var(--ion-text-color);
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 0.25rem 0;
}

.child-grade {
  color: var(--ion-color-primary);
  font-size: 0.875rem;
  font-weight: 500;
  margin: 0 0 0.25rem 0;
}

.child-school {
  color: var(--ion-color-medium);
  font-size: 0.8125rem;
  margin: 0;
}

.child-badge {
  --background: var(--ion-color-success);
  --color: var(--ion-color-success-contrast);
  font-size: 0.75rem;
  font-weight: 600;
}

/* Section Titles */
.section-title {
  --color: var(--ion-text-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
}

.section-icon {
  color: var(--ion-color-primary);
  font-size: 1.25rem;
}

/* Contact Information Card */
.contact-card {
  --background: var(--ion-card-background);
  --color: var(--ion-card-color);
  margin-bottom: 1rem;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
}

.contact-list {
  --background: transparent;
  --color: inherit;
}

.contact-item {
  --background: transparent;
  --color: inherit;
  --padding-start: 0;
  --padding-end: 0;
  --inner-padding-start: 0;
  --inner-padding-end: 0;
  margin-bottom: 1rem;
}

.contact-item:last-child {
  margin-bottom: 0;
}

.contact-icon {
  color: var(--ion-color-primary);
  font-size: 1.25rem;
  margin-right: 1rem;
}

.contact-label {
  color: var(--ion-color-medium);
  font-size: 0.875rem;
  font-weight: 500;
  margin: 0 0 0.25rem 0;
}

.contact-value {
  color: var(--ion-text-color);
  font-size: 1rem;
  font-weight: 500;
  margin: 0;
}

/* Settings & Preferences Card */
.settings-card {
  --background: var(--ion-card-background);
  --color: var(--ion-card-color);
  margin-bottom: 1rem;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
}

.settings-list {
  --background: transparent;
  --color: inherit;
}

.settings-item {
  --background: transparent;
  --color: inherit;
  --padding-start: 0;
  --padding-end: 0;
  --inner-padding-start: 0;
  --inner-padding-end: 0;
  margin-bottom: 0.5rem;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.settings-item:hover {
  --background: var(--ion-color-light);
  transform: translateX(4px);
}

.settings-icon {
  color: var(--ion-color-primary);
  font-size: 1.25rem;
  margin-right: 1rem;
}

.chevron-icon {
  color: var(--ion-color-medium);
  font-size: 1rem;
}

/* Theme Toggle Integration */
.theme-toggle-item {
  margin-bottom: 0.5rem;
}

/* Support & Legal Card */
.support-card {
  --background: var(--ion-card-background);
  --color: var(--ion-card-color);
  margin-bottom: 1.5rem;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
}

.support-list {
  --background: transparent;
  --color: inherit;
}

.support-item {
  --background: transparent;
  --color: inherit;
  --padding-start: 0;
  --padding-end: 0;
  --inner-padding-start: 0;
  --inner-padding-end: 0;
  margin-bottom: 0.5rem;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.support-item:hover {
  --background: var(--ion-color-light);
  transform: translateX(4px);
}

.support-icon {
  color: var(--ion-color-tertiary);
  font-size: 1.25rem;
  margin-right: 1rem;
}

/* Logout Section */
.logout-section {
  padding: 0 1rem 1rem;
  margin-top: 1rem;
}

.logout-button {
  --border-radius: 12px;
  --border-width: 2px;
  --border-color: var(--ion-color-danger);
  --color: var(--ion-color-danger);
  --background: transparent;
  font-weight: 600;
  height: 48px;
  transition: all 0.3s ease;
}

.logout-button:hover {
  --background: var(--ion-color-danger);
  --color: var(--ion-color-danger-contrast);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--ion-color-danger-rgb), 0.3);
}

/* Dark Mode Support */
.ion-palette-dark .child-item {
  background: var(--ion-color-dark);
  border: 1px solid var(--ion-color-dark-shade);
}

.ion-palette-dark .child-item:hover {
  background: var(--ion-color-dark-tint);
}

.ion-palette-dark .settings-item:hover,
.ion-palette-dark .support-item:hover {
  --background: var(--ion-color-dark);
}

.ion-palette-dark .profile-header-card,
.ion-palette-dark .children-card,
.ion-palette-dark .contact-card,
.ion-palette-dark .settings-card,
.ion-palette-dark .support-card {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .account-content {
    --padding-start: 0.75rem;
    --padding-end: 0.75rem;
  }

  .profile-header-content {
    padding: 1.25rem;
  }

  .profile-main {
    gap: 0.75rem;
  }

  .profile-avatar {
    width: 70px;
    height: 70px;
  }

  .profile-name {
    font-size: 1.375rem;
  }

  .edit-profile-btn {
    width: 36px;
    height: 36px;
  }
}

@media (max-width: 480px) {
  .account-content {
    --padding-start: 0.5rem;
    --padding-end: 0.5rem;
  }

  .profile-header-content {
    padding: 1rem;
  }

  .profile-main {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 1rem;
  }

  .profile-avatar {
    width: 80px;
    height: 80px;
  }

  .profile-name {
    font-size: 1.25rem;
  }

  .edit-profile-btn {
    position: static;
    margin-top: 0.5rem;
  }

  .child-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .child-badge {
    align-self: flex-end;
  }

  .section-title {
    font-size: 1rem;
  }

  .section-icon {
    font-size: 1.125rem;
  }
}

@media (max-width: 360px) {
  .profile-header-content {
    padding: 0.875rem;
  }

  .child-item {
    padding: 0.875rem;
  }

  .contact-item,
  .settings-item,
  .support-item {
    padding: 0.75rem 0;
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  .child-item,
  .settings-item,
  .support-item,
  .logout-button,
  .edit-profile-btn {
    transition: none;
  }

  .child-item:hover,
  .settings-item:hover,
  .support-item:hover,
  .logout-button:hover {
    transform: none;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .profile-header-card,
  .children-card,
  .contact-card,
  .settings-card,
  .support-card {
    border: 2px solid var(--ion-text-color);
  }

  .child-item {
    border: 1px solid var(--ion-color-medium);
  }

  .role-chip {
    border: 1px solid var(--ion-color-primary);
  }

  .child-badge {
    border: 1px solid var(--ion-color-success);
  }
}

/* Focus Styles for Accessibility */
.edit-profile-btn:focus,
.settings-item:focus,
.support-item:focus,
.logout-button:focus {
  outline: 2px solid var(--ion-color-primary);
  outline-offset: 2px;
}

/* Touch Target Optimization */
.settings-item,
.support-item {
  min-height: 48px;
  padding: 0.75rem 0;
}

.edit-profile-btn {
  min-width: 44px;
  min-height: 44px;
}

/* Loading States */
.profile-avatar img {
  transition: opacity 0.3s ease;
}

.profile-avatar img[src=""] {
  opacity: 0;
}

/* Card Hover Effects */
.profile-header-card:hover,
.children-card:hover,
.contact-card:hover,
.settings-card:hover,
.support-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.12);
}

.ion-palette-dark .profile-header-card:hover,
.ion-palette-dark .children-card:hover,
.ion-palette-dark .contact-card:hover,
.ion-palette-dark .settings-card:hover,
.ion-palette-dark .support-card:hover {
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.4);
}