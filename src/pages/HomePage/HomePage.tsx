import { IonIcon, IonLabel, IonRouterOutlet, IonTabBar, IonTabButton, IonTabs } from '@ionic/react';
import { Redirect, Route } from 'react-router-dom';
import Tab1 from './Tab1';
import Tab2 from './Tab2';
import Tab3 from './Tab3';
import CatalogoTab from './CatalogoTab';
import AccountPage from '../AccountPage/AccountPage';
import { ROUTES } from '../../routes/routes';
import './HomePage.css';

// Import SVG icons
import HomeIcon from '../../assets/icons/home.svg';
import ReportsIcon from '../../assets/icons/reports.svg';
import ConnectionIcon from '../../assets/icons/connection.svg';
import ResourcesIcon from '../../assets/icons/resources.svg';
import CatalogIcon from '../../assets/icons/catalog.svg';
import AccountIcon from '../../assets/icons/account.svg';

const HomePage: React.FC = () => {
  return (
    <IonTabs>
      <IonRouterOutlet>
        <Route exact path={ROUTES.TABS.HOME}>
          <Tab1 />
        </Route>
        <Route exact path={ROUTES.TABS.REPORTS}>
          <Tab2 />
        </Route>
        <Route exact path={ROUTES.TABS.CONNECTION}>
          <Tab3 />
        </Route>
        <Route exact path={ROUTES.TABS.RESOURCES}>
          <Tab3 />
        </Route>
        <Route exact path={ROUTES.TABS.CATALOG}>
          <CatalogoTab />
        </Route>
        <Route exact path={ROUTES.TABS.ACCOUNT}>
          <AccountPage />
        </Route>
        <Route exact path={ROUTES.HOME}>
          <Redirect to={ROUTES.TABS.HOME} />
        </Route>
      </IonRouterOutlet>

      <IonTabBar slot="bottom">
        <IonTabButton tab="inicio" href={ROUTES.TABS.HOME}>
          <IonIcon icon={HomeIcon} />
          <IonLabel>Inicio</IonLabel>
        </IonTabButton>
        <IonTabButton tab="informes" href={ROUTES.TABS.REPORTS}>
          <IonIcon icon={ReportsIcon} />
          <IonLabel>Informes</IonLabel>
        </IonTabButton>
        <IonTabButton tab="conexion" href={ROUTES.TABS.CONNECTION}>
          <IonIcon icon={ConnectionIcon} />
          <IonLabel>Conexión</IonLabel>
        </IonTabButton>
        <IonTabButton tab="recursos" href={ROUTES.TABS.RESOURCES}>
          <IonIcon icon={ResourcesIcon} />
          <IonLabel>Recursos</IonLabel>
        </IonTabButton>
        <IonTabButton tab="catalogo" href={ROUTES.TABS.CATALOG}>
          <IonIcon icon={CatalogIcon} />
          <IonLabel>Catálogo</IonLabel>
        </IonTabButton>
        <IonTabButton tab="cuenta" href={ROUTES.TABS.ACCOUNT}>
          <IonIcon icon={AccountIcon} />
          <IonLabel>Cuenta</IonLabel>
        </IonTabButton>
      </IonTabBar>
    </IonTabs>
  );
};

export default HomePage;
