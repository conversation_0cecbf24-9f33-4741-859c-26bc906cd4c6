/* CatalogoTab.css */
.catalogo-content {
  --background: var(--ion-background-color);
  background: linear-gradient(180deg,
    var(--ion-background-color) 0%,
    var(--ion-color-step-50) 100%);
}

.catalogo-container {
  position: relative;
  min-height: 100vh;
}

/* Background decorative elements */
.catalogo-container::before {
  content: '';
  position: absolute;
  top: -100px;
  right: -100px;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, color-mix(in srgb, var(--ion-color-secondary) 5%, transparent) 0%, transparent 70%);
  border-radius: 50%;
  pointer-events: none;
  z-index: 0;
}

.catalogo-title-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 26px 0;
  position: relative;
  z-index: 1;
}

.catalogo-title {
  font-family: 'Inter', sans-serif;
  font-weight: 700;
  font-size: 18px;
  line-height: 1.21;
  text-align: center;
  color: var(--fallback-bc, oklch(var(--bc)/1));
  margin: 0;
}

.catalogo-divider {
  width: 342px;
  height: 0.5px;
  background: var(--ion-border-color);
  max-width: calc(100vw - 32px);
}

.institutions-section {
  padding: 0 25px 40px;
  position: relative;
  z-index: 1;
}

/* Loading skeleton styles */
.loading-container {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.institution-skeleton {
  display: flex;
  flex-direction: column;
  gap: 12px;
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.skeleton-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 32px;
}

.skeleton-text {
  background: var(--ion-color-step-200);
  border-radius: 4px;
}

.skeleton-title {
  height: 16px;
  width: 70%;
  margin-bottom: 8px;
}

.skeleton-details {
  height: 12px;
  width: 50%;
}

.skeleton-badge {
  width: 48px;
  height: 32px;
  background: var(--ion-color-step-200);
  border-radius: 8px;
  flex-shrink: 0;
}

.skeleton-product {
  background: var(--ion-color-step-100);
  border-radius: 24px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.skeleton-image {
  width: 100%;
  height: 188px;
  background: var(--ion-color-step-200);
  border-radius: 12px;
}

.skeleton-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.skeleton-product-title {
  height: 16px;
  width: 60%;
}

.skeleton-product-desc {
  height: 12px;
  width: 40%;
}

.skeleton-price {
  height: 20px;
  width: 30%;
  margin-top: 8px;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .catalogo-title-section {
    padding: 20px 0;
    gap: 12px;
  }
  
  .catalogo-title {
    font-size: 16px;
  }
  
  .catalogo-divider {
    width: 300px;
  }
  
  .institutions-section {
    padding: 0 16px 32px;
  }
  
  .loading-container {
    gap: 24px;
  }
  
  .institution-skeleton {
    gap: 8px;
  }
  
  .skeleton-header {
    gap: 16px;
    flex-direction: column;
    align-items: flex-start;
  }
  
  .skeleton-badge {
    width: 40px;
    height: 28px;
  }
  
  .skeleton-product {
    padding: 12px;
    gap: 12px;
  }
  
  .skeleton-image {
    height: 160px;
  }
}

@media (max-width: 480px) {
  .catalogo-title-section {
    padding: 16px 0;
    gap: 10px;
  }
  
  .catalogo-title {
    font-size: 15px;
  }
  
  .catalogo-divider {
    width: 280px;
  }
  
  .institutions-section {
    padding: 0 12px 24px;
  }
  
  .loading-container {
    gap: 20px;
  }
  
  .skeleton-header {
    gap: 12px;
  }
  
  .skeleton-title {
    height: 14px;
  }
  
  .skeleton-details {
    height: 10px;
  }
  
  .skeleton-badge {
    width: 36px;
    height: 24px;
  }
  
  .skeleton-product {
    padding: 10px;
    gap: 10px;
  }
  
  .skeleton-image {
    height: 140px;
  }
  
  .skeleton-product-title {
    height: 14px;
  }
  
  .skeleton-product-desc {
    height: 10px;
  }
  
  .skeleton-price {
    height: 18px;
  }
}



/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .catalogo-content {
    background: var(--ion-background-color);
  }

  .institution-skeleton {
    animation: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .catalogo-container::before {
    display: none;
  }
  
  .catalogo-divider {
    background: var(--ion-text-color);
    height: 1px;
  }

  .skeleton-text,
  .skeleton-badge,
  .skeleton-image,
  .skeleton-product {
    border: 1px solid var(--ion-border-color);
  }
}
