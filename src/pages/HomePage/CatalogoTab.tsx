import React, { useState, useEffect } from 'react';
import { IonContent, IonPage } from '@ionic/react';
import GlobalHeader from '../../components/GlobalHeader/GlobalHeader';
import InstitutionCard from '../../components/InstitutionCard/InstitutionCard';
import './CatalogoTab.css';

interface ProductData {
  id: string;
  name: string;
  description: string;
  price: number;
  installments: string;
  image: string;
  kitType: 'customizable' | 'complete';
  itemCount: number;
  publisher: string;
}

interface InstitutionData {
  id: string;
  name: string;
  details: string;
  year: string;
  products: ProductData[];
}

const CatalogoTab: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [institutions, setInstitutions] = useState<InstitutionData[]>([]);

  useEffect(() => {
    const loadCatalogData = async () => {
      try {
        // Mock catalog data based on Figma design
        const mockInstitutions: InstitutionData[] = [
          {
            id: '1',
            name: 'INST DE ENS COL AMORIM SOCIED SIMPLES LT (TESTE17- COMP TESTE)',
            details: '1º año EM | Turno A - Matutino | Ensino Médio',
            year: '2025',
            products: [
              {
                id: 'p1',
                name: '1EM - LISTA 1EM 25',
                description: '7x de R$200,09 com juros',
                price: 1366.40,
                installments: '7x de R$200,09 com juros',
                image: '/api/placeholder/343/188',
                kitType: 'customizable',
                itemCount: 4,
                publisher: 'Compartilha'
              }
            ]
          },
          {
            id: '2',
            name: 'INST DE ENS COL AMORIM SOCIED SIMPLES LT (TESTE17- RSOLUT)',
            details: '1º año EM | Turno A - Matutino | Ensino Médio',
            year: '2025',
            products: [
              {
                id: 'p2',
                name: 'GO 1EM 25 - GO 1EM 25',
                description: '7x de R$125,35 com juros',
                price: 856.00,
                installments: '7x de R$125,35 com juros',
                image: '/api/placeholder/343/188',
                kitType: 'complete',
                itemCount: 1,
                publisher: 'Richmond Solutions'
              }
            ]
          }
        ];

        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        setInstitutions(mockInstitutions);
      } catch (error) {
        console.error('Error loading catalog data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadCatalogData();
  }, []);

  const handleAddToCart = (productId: string) => {
    console.log('Adding product to cart:', productId);
    // TODO: Implement add to cart functionality
  };

  const handleShare = (productId: string) => {
    console.log('Sharing product:', productId);
    // TODO: Implement share functionality
  };

  return (
    <IonPage>
      <GlobalHeader title="Catálogo" showBackButton={true} />
      <IonContent fullscreen className="catalogo-content">
        <div className="catalogo-container">
          
          <div className="catalogo-title-section">
            <h2 className="catalogo-title">Lista de compras</h2>
            <div className="catalogo-divider"></div>
          </div>

          <div className="institutions-section">
            {isLoading ? (
              <div className="loading-container">
                {[...Array(2)].map((_, index) => (
                  <div key={index} className="institution-skeleton">
                    <div className="skeleton-header">
                      <div className="skeleton-text skeleton-title"></div>
                      <div className="skeleton-text skeleton-details"></div>
                      <div className="skeleton-badge"></div>
                    </div>
                    <div className="skeleton-product">
                      <div className="skeleton-image"></div>
                      <div className="skeleton-content">
                        <div className="skeleton-text skeleton-product-title"></div>
                        <div className="skeleton-text skeleton-product-desc"></div>
                        <div className="skeleton-price"></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              institutions.map((institution) => (
                <InstitutionCard
                  key={institution.id}
                  institution={institution}
                  onAddToCart={handleAddToCart}
                  onShare={handleShare}
                />
              ))
            )}
          </div>
        </div>
      </IonContent>
    </IonPage>
  );
};

export default CatalogoTab;
