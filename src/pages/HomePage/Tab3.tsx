import { IonContent, IonPage } from '@ionic/react';
import { useLocation } from 'react-router-dom';
import GlobalHeader from '../../components/GlobalHeader/GlobalHeader';
import ExploreContainer from '../../components/ExploreContainer';
import { ROUTES } from '../../routes/routes';
import './Tab3.css';

const Tab3: React.FC = () => {
  const location = useLocation();

  // Determine title based on current route
  const getTitle = () => {
    if (location.pathname === ROUTES.TABS.CONNECTION) {
      return 'Conexión';
    } else if (location.pathname === ROUTES.TABS.RESOURCES) {
      return 'Recursos';
    }
    return 'Tab 3';
  };

  return (
    <IonPage>
      <GlobalHeader title={getTitle()} />
      <IonContent fullscreen>
        <ExploreContainer name="Tab 3 page" />
      </IonContent>
    </IonPage>
  );
};

export default Tab3;
