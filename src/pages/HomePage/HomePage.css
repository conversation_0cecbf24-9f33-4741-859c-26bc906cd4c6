/* Custom tab bar styles */
ion-tab-bar {
  --background: var(--ion-color-light);
  box-shadow: 0px -1px 10px 1px rgba(0, 0, 0, 0.1);
  border-radius: 78px;
  margin: 10px;
  padding: 10px 20px;
  border: unset;
}

ion-tab-button {
  --color: var(--ion-color-dark);
  --color-selected: var(--ion-color-secondary);
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: 10px;
  line-height: 1.21em;
}

ion-tab-button ion-icon {
  stroke: currentColor;
  fill: none;
  stroke-width: 1.5px;
  width: 24px;
  height: 24px;
}