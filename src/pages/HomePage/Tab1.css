/* Tab1.css */
.tab1-content {
  --background: var(--ion-background-color);
  background: linear-gradient(180deg,
    var(--ion-background-color) 0%,
    var(--ion-color-step-50) 100%);
}

.tab1-container {
  padding: 1rem;
  max-width: 100%;
  margin: 0 auto;
  position: relative;
}

/* Background decorative elements */
.tab1-container::before {
  content: '';
  position: absolute;
  top: -50px;
  right: -50px;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, color-mix(in srgb, var(--ion-color-secondary) 10%, transparent) 0%, transparent 70%);
  border-radius: 50%;
  pointer-events: none;
  z-index: 0;
}

.tab1-container::after {
  content: '';
  position: absolute;
  bottom: -100px;
  left: -100px;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, color-mix(in srgb, var(--ion-color-primary) 8%, transparent) 0%, transparent 70%);
  border-radius: 50%;
  pointer-events: none;
  z-index: 0;
}

.cards-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  position: relative;
  z-index: 1;
}

/* Responsive design */
@media (min-width: 768px) {
  .tab1-container {
    padding: 2rem;
    max-width: 768px;
  }

  .cards-section {
    gap: 2rem;
  }
}

@media (min-width: 1024px) {
  .tab1-container {
    max-width: 1024px;
  }
}



/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .tab1-content {
    background: var(--ion-background-color);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .tab1-container::before,
  .tab1-container::after {
    display: none;
  }
}