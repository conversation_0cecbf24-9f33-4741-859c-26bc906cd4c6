import { IonContent, IonPage } from '@ionic/react';
import GlobalHeader from '../../components/GlobalHeader/GlobalHeader';
import ExploreContainer from '../../components/ExploreContainer';
import './Tab2.css';

const Tab2: React.FC = () => {
  return (
    <IonPage>
      <GlobalHeader title="Informes" />
      <IonContent fullscreen>
        <ExploreContainer name="Tab 2 page" />
      </IonContent>
    </IonPage>
  );
};

export default Tab2;
