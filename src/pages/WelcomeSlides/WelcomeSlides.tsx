import React from 'react';
import {
  IonContent,
  IonPage,
  IonButton,
  IonIcon,
} from '@ionic/react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Pagination } from 'swiper/modules';
import { schoolOutline, peopleOutline, calendarOutline } from 'ionicons/icons';
import { useHistory } from 'react-router-dom';
import 'swiper/css';
import 'swiper/css/pagination';
import './WelcomeSlides.css';

const slideOpts = {
  initialSlide: 0,
  speed: 400,
  pagination: true,
  modules: [Pagination]
};

const WelcomeSlides: React.FC = () => {
  const history = useHistory();

  const handleFinish = () => {
    // Marcar que el usuario ya vio los slides
    localStorage.setItem('hasSeenWelcome', 'true');
    // Redirigir a la página de auth
    history.replace('/auth');
  };

  return (
    <IonPage>
      <IonContent fullscreen className="welcome-slides">
        <Swiper {...slideOpts}>
          <SwiperSlide>
            <div className="slide-content">
              <div className="icon-container">
                <IonIcon icon={schoolOutline} />
              </div>
              <h2>Bienvenido a Agenda Familiar</h2>
              <p>Tu herramienta para mantenerte conectado con la educación de tus hijos</p>
            </div>
          </SwiperSlide>

          <SwiperSlide>
            <div className="slide-content">
              <div className="icon-container">
                <IonIcon icon={peopleOutline} />
              </div>
              <h2>Mantente Conectado</h2>
              <p>Comunícate fácilmente con profesores y mantente al día con las actividades escolares</p>
            </div>
          </SwiperSlide>

          <SwiperSlide>
            <div className="slide-content">
              <div className="icon-container">
                <IonIcon icon={calendarOutline} />
              </div>
              <h2>Organiza Todo</h2>
              <p>Gestiona horarios, tareas y eventos importantes en un solo lugar</p>
              <IonButton onClick={handleFinish}>
                Comenzar
              </IonButton>
            </div>
          </SwiperSlide>
        </Swiper>
      </IonContent>
    </IonPage>
  );
};

export default WelcomeSlides; 