/**
 * Crypto fallback utility for PKCE code challenge generation
 * Provides Web Crypto API with fallback for environments where crypto.subtle is not available
 */

import { Capacitor } from '@capacitor/core';

export class CryptoFallback {
  /**
   * Check if Web Crypto API is available
   */
  static isWebCryptoAvailable(): boolean {
    try {
      return !!(window.crypto && window.crypto.subtle && window.crypto.subtle.digest);
    } catch (error) {
      return false;
    }
  }

  /**
   * Generate SHA256 hash using Web Crypto API or fallback
   */
  static async sha256(data: string): Promise<ArrayBuffer> {
    const encoder = new TextEncoder();
    const dataBuffer = encoder.encode(data);

    // Try Web Crypto API first
    if (this.isWebCryptoAvailable()) {
      try {
        console.log('🔐 [CRYPTO] Using Web Crypto API for SHA256');
        return await window.crypto.subtle.digest('SHA-256', dataBuffer);
      } catch (error) {
        console.warn('⚠️ [CRYPTO] Web Crypto API failed, falling back to JS implementation:', error);
      }
    }

    // Fallback to JavaScript implementation
    console.log('🔐 [CRYPTO] Using JavaScript fallback for SHA256');
    return this.sha256Fallback(dataBuffer);
  }

  /**
   * JavaScript implementation of SHA256 (fallback)
   * Based on RFC 6234 specification
   */
  private static sha256Fallback(data: Uint8Array): ArrayBuffer {
    // SHA256 constants
    const K = new Uint32Array([
      0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,
      0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,
      0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,
      0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,
      0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,
      0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,
      0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,
      0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2
    ]);

    // Initial hash values
    let h0 = 0x6a09e667;
    let h1 = 0xbb67ae85;
    let h2 = 0x3c6ef372;
    let h3 = 0xa54ff53a;
    let h4 = 0x510e527f;
    let h5 = 0x9b05688c;
    let h6 = 0x1f83d9ab;
    let h7 = 0x5be0cd19;

    // Pre-processing
    const msgLength = data.length;
    const bitLength = msgLength * 8;

    // Append padding
    const paddedLength = Math.ceil((msgLength + 9) / 64) * 64;
    const padded = new Uint8Array(paddedLength);
    padded.set(data);
    padded[msgLength] = 0x80;

    // Append length as 64-bit big-endian
    const view = new DataView(padded.buffer);
    view.setUint32(paddedLength - 4, bitLength & 0xffffffff, false);
    view.setUint32(paddedLength - 8, Math.floor(bitLength / 0x100000000), false);

    // Process chunks
    for (let chunkStart = 0; chunkStart < paddedLength; chunkStart += 64) {
      const w = new Uint32Array(64);

      // Copy chunk into first 16 words
      for (let i = 0; i < 16; i++) {
        w[i] = view.getUint32(chunkStart + i * 4, false);
      }

      // Extend the first 16 words into the remaining 48 words
      for (let i = 16; i < 64; i++) {
        const s0 = this.rightRotate(w[i - 15], 7) ^ this.rightRotate(w[i - 15], 18) ^ (w[i - 15] >>> 3);
        const s1 = this.rightRotate(w[i - 2], 17) ^ this.rightRotate(w[i - 2], 19) ^ (w[i - 2] >>> 10);
        w[i] = (w[i - 16] + s0 + w[i - 7] + s1) >>> 0;
      }

      // Initialize working variables
      let a = h0, b = h1, c = h2, d = h3, e = h4, f = h5, g = h6, h = h7;

      // Main loop
      for (let i = 0; i < 64; i++) {
        const S1 = this.rightRotate(e, 6) ^ this.rightRotate(e, 11) ^ this.rightRotate(e, 25);
        const ch = (e & f) ^ (~e & g);
        const temp1 = (h + S1 + ch + K[i] + w[i]) >>> 0;
        const S0 = this.rightRotate(a, 2) ^ this.rightRotate(a, 13) ^ this.rightRotate(a, 22);
        const maj = (a & b) ^ (a & c) ^ (b & c);
        const temp2 = (S0 + maj) >>> 0;

        h = g;
        g = f;
        f = e;
        e = (d + temp1) >>> 0;
        d = c;
        c = b;
        b = a;
        a = (temp1 + temp2) >>> 0;
      }

      // Add this chunk's hash to result
      h0 = (h0 + a) >>> 0;
      h1 = (h1 + b) >>> 0;
      h2 = (h2 + c) >>> 0;
      h3 = (h3 + d) >>> 0;
      h4 = (h4 + e) >>> 0;
      h5 = (h5 + f) >>> 0;
      h6 = (h6 + g) >>> 0;
      h7 = (h7 + h) >>> 0;
    }

    // Produce the final hash value as ArrayBuffer
    const result = new ArrayBuffer(32);
    const resultView = new DataView(result);
    resultView.setUint32(0, h0, false);
    resultView.setUint32(4, h1, false);
    resultView.setUint32(8, h2, false);
    resultView.setUint32(12, h3, false);
    resultView.setUint32(16, h4, false);
    resultView.setUint32(20, h5, false);
    resultView.setUint32(24, h6, false);
    resultView.setUint32(28, h7, false);

    return result;
  }

  /**
   * Right rotate helper function
   */
  private static rightRotate(value: number, amount: number): number {
    return (value >>> amount) | (value << (32 - amount));
  }

  /**
   * Convert ArrayBuffer to base64url string
   */
  static arrayBufferToBase64Url(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    const base64 = btoa(binary);
    return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
  }

  /**
   * Generate PKCE code challenge from code verifier
   */
  static async generateCodeChallenge(codeVerifier: string): Promise<string> {
    try {
      console.log('🔐 [CRYPTO] Generating PKCE code challenge');
      console.log('🔐 [CRYPTO] Web Crypto available:', this.isWebCryptoAvailable());
      console.log('🔐 [CRYPTO] Platform:', Capacitor.getPlatform());

      const hash = await this.sha256(codeVerifier);
      const challenge = this.arrayBufferToBase64Url(hash);

      console.log('🔐 [CRYPTO] Code challenge generated successfully');
      console.log('🔐 [CRYPTO] Challenge length:', challenge.length);
      console.log('🔐 [CRYPTO] Challenge preview:', challenge.substring(0, 20) + '...');

      return challenge;
    } catch (error) {
      console.error('❌ [CRYPTO] Error generating code challenge:', error);
      throw new Error(`Failed to generate PKCE code challenge: ${error}`);
    }
  }

  /**
   * Generate cryptographically secure random string
   */
  static generateRandomString(length: number): string {
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
    let result = '';

    try {
      // Try to use crypto.getRandomValues if available
      if (window.crypto && window.crypto.getRandomValues) {
        const values = new Uint8Array(length);
        window.crypto.getRandomValues(values);
        for (let i = 0; i < length; i++) {
          result += charset[values[i] % charset.length];
        }
      } else {
        // Fallback to Math.random (less secure but functional)
        console.warn('⚠️ [CRYPTO] crypto.getRandomValues not available, using Math.random fallback');
        for (let i = 0; i < length; i++) {
          result += charset[Math.floor(Math.random() * charset.length)];
        }
      }
    } catch (error) {
      console.error('❌ [CRYPTO] Error generating random string:', error);
      // Ultimate fallback
      for (let i = 0; i < length; i++) {
        result += charset[Math.floor(Math.random() * charset.length)];
      }
    }

    return result;
  }

  /**
   * Test crypto functionality
   */
  static async testCrypto(): Promise<void> {
    console.log('🧪 [CRYPTO] Testing crypto functionality...');
    console.log('🧪 [CRYPTO] Web Crypto available:', this.isWebCryptoAvailable());

    try {
      const testString = 'test-code-verifier-' + Date.now();
      const challenge = await this.generateCodeChallenge(testString);
      console.log('🧪 [CRYPTO] Test successful - challenge generated:', challenge.substring(0, 20) + '...');
    } catch (error) {
      console.error('🧪 [CRYPTO] Test failed:', error);
      throw error;
    }
  }
}
