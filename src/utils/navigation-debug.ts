/**
 * Navigation debugging utilities
 * Helps track and debug navigation issues in the app
 */

export class NavigationDebug {
  /**
   * Log current navigation state
   */
  static logNavigationState(context: string) {
    console.log(`🧭 [NAV-DEBUG] ${context}:`, {
      currentPath: window.location.pathname,
      currentSearch: window.location.search,
      currentHash: window.location.hash,
      currentHref: window.location.href,
      historyLength: window.history.length,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Test navigation capabilities
   */
  static testNavigation() {
    console.log('🧪 [NAV-DEBUG] Testing navigation capabilities:');
    
    // Test history API
    console.log('🧪 [NAV-DEBUG] History API:', {
      hasHistory: !!(window.history),
      hasPushState: !!(window.history && window.history.pushState),
      hasReplaceState: !!(window.history && window.history.replaceState),
      hasBack: !!(window.history && window.history.back),
      hasForward: !!(window.history && window.history.forward),
      hasGo: !!(window.history && window.history.go)
    });

    // Test location API
    console.log('🧪 [NAV-DEBUG] Location API:', {
      hasLocation: !!(window.location),
      hasAssign: !!(window.location && window.location.assign),
      hasReplace: !!(window.location && window.location.replace),
      hasReload: !!(window.location && window.location.reload),
      canSetHref: true // Always available
    });

    // Test React Router compatibility
    console.log('🧪 [NAV-DEBUG] Current state:', {
      pathname: window.location.pathname,
      search: window.location.search,
      hash: window.location.hash,
      state: window.history.state
    });
  }

  /**
   * Attempt navigation with multiple fallback methods
   */
  static async attemptNavigation(targetPath: string, history?: any) {
    console.log(`🧭 [NAV-DEBUG] Attempting navigation to: ${targetPath}`);
    
    this.logNavigationState('Before navigation');

    let navigationSuccess = false;

    // Method 1: React Router history.replace
    if (history && history.replace) {
      try {
        console.log('🧭 [NAV-DEBUG] Trying history.replace');
        history.replace(targetPath);
        
        // Check if navigation worked after a short delay
        setTimeout(() => {
          if (window.location.pathname === targetPath) {
            console.log('✅ [NAV-DEBUG] history.replace successful');
            navigationSuccess = true;
          } else {
            console.log('❌ [NAV-DEBUG] history.replace failed');
            this.tryFallbackNavigation(targetPath);
          }
        }, 100);
        
        return;
      } catch (error) {
        console.error('❌ [NAV-DEBUG] history.replace error:', error);
      }
    }

    // Method 2: React Router history.push
    if (history && history.push) {
      try {
        console.log('🧭 [NAV-DEBUG] Trying history.push');
        history.push(targetPath);
        
        setTimeout(() => {
          if (window.location.pathname === targetPath) {
            console.log('✅ [NAV-DEBUG] history.push successful');
            navigationSuccess = true;
          } else {
            console.log('❌ [NAV-DEBUG] history.push failed');
            this.tryFallbackNavigation(targetPath);
          }
        }, 100);
        
        return;
      } catch (error) {
        console.error('❌ [NAV-DEBUG] history.push error:', error);
      }
    }

    // If no React Router history available, try fallback methods
    this.tryFallbackNavigation(targetPath);
  }

  /**
   * Try fallback navigation methods
   */
  private static tryFallbackNavigation(targetPath: string) {
    console.log('🧭 [NAV-DEBUG] Trying fallback navigation methods');

    // Method 3: window.history.pushState + popstate
    try {
      console.log('🧭 [NAV-DEBUG] Trying window.history.pushState');
      window.history.pushState({}, '', targetPath);
      
      // Trigger popstate event to notify React Router
      const popstateEvent = new PopStateEvent('popstate', { state: {} });
      window.dispatchEvent(popstateEvent);
      
      setTimeout(() => {
        if (window.location.pathname === targetPath) {
          console.log('✅ [NAV-DEBUG] window.history.pushState successful');
          return;
        } else {
          console.log('❌ [NAV-DEBUG] window.history.pushState failed');
          this.tryDirectNavigation(targetPath);
        }
      }, 100);
      
      return;
    } catch (error) {
      console.error('❌ [NAV-DEBUG] window.history.pushState error:', error);
    }

    // Method 4: Direct navigation
    this.tryDirectNavigation(targetPath);
  }

  /**
   * Try direct navigation methods
   */
  private static tryDirectNavigation(targetPath: string) {
    console.log('🧭 [NAV-DEBUG] Trying direct navigation methods');

    // Method 4: window.location.assign
    try {
      console.log('🧭 [NAV-DEBUG] Trying window.location.assign');
      window.location.assign(targetPath);
      return;
    } catch (error) {
      console.error('❌ [NAV-DEBUG] window.location.assign error:', error);
    }

    // Method 5: window.location.href (last resort)
    try {
      console.log('🧭 [NAV-DEBUG] Trying window.location.href (last resort)');
      window.location.href = targetPath;
    } catch (error) {
      console.error('❌ [NAV-DEBUG] window.location.href error:', error);
      console.error('🚨 [NAV-DEBUG] All navigation methods failed!');
    }
  }

  /**
   * Monitor navigation changes
   */
  static startNavigationMonitoring() {
    console.log('🧭 [NAV-DEBUG] Starting navigation monitoring');

    // Monitor popstate events
    window.addEventListener('popstate', (event) => {
      console.log('🧭 [NAV-DEBUG] popstate event:', {
        state: event.state,
        pathname: window.location.pathname
      });
    });

    // Monitor hashchange events
    window.addEventListener('hashchange', (event) => {
      console.log('🧭 [NAV-DEBUG] hashchange event:', {
        oldURL: event.oldURL,
        newURL: event.newURL
      });
    });

    // Monitor beforeunload events
    window.addEventListener('beforeunload', (event) => {
      console.log('🧭 [NAV-DEBUG] beforeunload event');
    });

    // Periodic location check
    let lastPathname = window.location.pathname;
    setInterval(() => {
      if (window.location.pathname !== lastPathname) {
        console.log('🧭 [NAV-DEBUG] Location changed:', {
          from: lastPathname,
          to: window.location.pathname
        });
        lastPathname = window.location.pathname;
      }
    }, 1000);
  }
}
