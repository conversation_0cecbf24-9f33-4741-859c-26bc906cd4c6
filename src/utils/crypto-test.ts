/**
 * Crypto fallback test utility
 * Use this to verify crypto functionality works in your environment
 */

import { CryptoFallback } from './crypto-fallback';

export class CryptoTest {
  /**
   * Run comprehensive crypto tests
   */
  static async runTests(): Promise<void> {
    console.log('🧪 [CRYPTO-TEST] Starting comprehensive crypto tests...');

    try {
      // Test 1: Check crypto availability
      console.log('🧪 [CRYPTO-TEST] Test 1: Crypto availability');
      const webCryptoAvailable = CryptoFallback.isWebCryptoAvailable();
      console.log('🧪 [CRYPTO-TEST] Web Crypto available:', webCryptoAvailable);

      // Test 2: Random string generation
      console.log('🧪 [CRYPTO-TEST] Test 2: Random string generation');
      const randomString = CryptoFallback.generateRandomString(32);
      console.log('🧪 [CRYPTO-TEST] Random string (32 chars):', randomString);
      console.log('🧪 [CRYPTO-TEST] Random string length:', randomString.length);

      // Test 3: PKCE code challenge generation
      console.log('🧪 [CRYPTO-TEST] Test 3: PKCE code challenge generation');
      const testVerifier = 'test-code-verifier-' + Date.now();
      const codeChallenge = await CryptoFallback.generateCodeChallenge(testVerifier);
      console.log('🧪 [CRYPTO-TEST] Code verifier:', testVerifier);
      console.log('🧪 [CRYPTO-TEST] Code challenge:', codeChallenge);
      console.log('🧪 [CRYPTO-TEST] Challenge length:', codeChallenge.length);

      // Test 4: Multiple challenge generations (should be different)
      console.log('🧪 [CRYPTO-TEST] Test 4: Multiple challenge generations');
      const challenge1 = await CryptoFallback.generateCodeChallenge('verifier1');
      const challenge2 = await CryptoFallback.generateCodeChallenge('verifier2');
      const challenge3 = await CryptoFallback.generateCodeChallenge('verifier1'); // Same as first
      
      console.log('🧪 [CRYPTO-TEST] Challenge 1:', challenge1);
      console.log('🧪 [CRYPTO-TEST] Challenge 2:', challenge2);
      console.log('🧪 [CRYPTO-TEST] Challenge 3 (same verifier as 1):', challenge3);
      console.log('🧪 [CRYPTO-TEST] Challenge 1 === Challenge 3:', challenge1 === challenge3);
      console.log('🧪 [CRYPTO-TEST] Challenge 1 !== Challenge 2:', challenge1 !== challenge2);

      // Test 5: Performance test
      console.log('🧪 [CRYPTO-TEST] Test 5: Performance test');
      const startTime = performance.now();
      for (let i = 0; i < 10; i++) {
        await CryptoFallback.generateCodeChallenge(`test-verifier-${i}`);
      }
      const endTime = performance.now();
      const avgTime = (endTime - startTime) / 10;
      console.log('🧪 [CRYPTO-TEST] Average time per challenge:', avgTime.toFixed(2), 'ms');

      console.log('✅ [CRYPTO-TEST] All tests passed successfully!');
      
    } catch (error) {
      console.error('❌ [CRYPTO-TEST] Test failed:', error);
      throw error;
    }
  }

  /**
   * Test specific crypto method
   */
  static async testMethod(method: 'web' | 'fallback'): Promise<void> {
    console.log(`🧪 [CRYPTO-TEST] Testing ${method} method specifically...`);

    try {
      if (method === 'web' && !CryptoFallback.isWebCryptoAvailable()) {
        console.warn('⚠️ [CRYPTO-TEST] Web Crypto not available, cannot test');
        return;
      }

      const testData = 'test-data-' + Date.now();
      
      if (method === 'web') {
        // Test Web Crypto directly
        const encoder = new TextEncoder();
        const data = encoder.encode(testData);
        const hash = await crypto.subtle.digest('SHA-256', data);
        const challenge = CryptoFallback.arrayBufferToBase64Url(hash);
        console.log('🧪 [CRYPTO-TEST] Web Crypto result:', challenge);
      } else {
        // Test fallback method
        const hash = await (CryptoFallback as any).sha256Fallback(new TextEncoder().encode(testData));
        const challenge = CryptoFallback.arrayBufferToBase64Url(hash);
        console.log('🧪 [CRYPTO-TEST] Fallback result:', challenge);
      }

      console.log(`✅ [CRYPTO-TEST] ${method} method test passed!`);
      
    } catch (error) {
      console.error(`❌ [CRYPTO-TEST] ${method} method test failed:`, error);
      throw error;
    }
  }

  /**
   * Compare Web Crypto vs Fallback results
   */
  static async compareResults(): Promise<void> {
    console.log('🧪 [CRYPTO-TEST] Comparing Web Crypto vs Fallback results...');

    if (!CryptoFallback.isWebCryptoAvailable()) {
      console.warn('⚠️ [CRYPTO-TEST] Web Crypto not available, cannot compare');
      return;
    }

    try {
      const testVerifier = 'test-comparison-verifier';

      // Generate using Web Crypto
      const encoder = new TextEncoder();
      const data = encoder.encode(testVerifier);
      const webHash = await crypto.subtle.digest('SHA-256', data);
      const webChallenge = CryptoFallback.arrayBufferToBase64Url(webHash);

      // Generate using fallback
      const fallbackHash = await (CryptoFallback as any).sha256Fallback(data);
      const fallbackChallenge = CryptoFallback.arrayBufferToBase64Url(fallbackHash);

      console.log('🧪 [CRYPTO-TEST] Web Crypto challenge:', webChallenge);
      console.log('🧪 [CRYPTO-TEST] Fallback challenge:', fallbackChallenge);
      console.log('🧪 [CRYPTO-TEST] Results match:', webChallenge === fallbackChallenge);

      if (webChallenge === fallbackChallenge) {
        console.log('✅ [CRYPTO-TEST] Web Crypto and Fallback produce identical results!');
      } else {
        console.error('❌ [CRYPTO-TEST] Web Crypto and Fallback produce different results!');
      }
      
    } catch (error) {
      console.error('❌ [CRYPTO-TEST] Comparison test failed:', error);
      throw error;
    }
  }
}

// Auto-run tests in development
if (process.env.NODE_ENV === 'development') {
  // Run tests after a short delay to ensure everything is loaded
  setTimeout(() => {
    CryptoTest.runTests().catch(console.error);
  }, 1000);
}
