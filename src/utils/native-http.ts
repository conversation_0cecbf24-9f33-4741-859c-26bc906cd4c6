/**
 * Native HTTP utility for Capacitor
 * Bypasses CORS restrictions by using official Capacitor HTTP plugin
 */

import { Capacitor, CapacitorHttp } from '@capacitor/core';
import type { HttpResponse, HttpOptions } from '@capacitor/core';

// Use the official HttpOptions interface from Capacitor core
// No need to define our own interface

export class NativeHttp {
  /**
   * Check if native HTTP is available
   * The official @capacitor/http is always available in Capacitor core
   */
  static isNativeHttpAvailable(): boolean {
    return Capacitor.isNativePlatform();
  }

  /**
   * Make HTTP request using official Capacitor HTTP plugin or fallback to fetch
   */
  static async request(options: HttpOptions): Promise<HttpResponse> {
    const { url, method, headers = {}, data, params } = options;

    console.log('🌐 [HTTP] Making HTTP request:', {
      url,
      method,
      isNative: this.isNativeHttpAvailable(),
      platform: Capacitor.getPlatform(),
      hasData: !!data,
      hasParams: !!params
    });

    try {
      if (this.isNativeHttpAvailable()) {
        console.log('🚀 [HTTP] Using official Capacitor HTTP (bypasses CORS)');

        // Use official Capacitor HTTP plugin
        const response = await CapacitorHttp.request({
          url,
          method,
          headers,
          data,
          params
        });

        console.log('✅ [HTTP] Native HTTP response received:', {
          status: response.status,
          statusText: response.statusText || 'OK',
          hasData: !!response.data,
          headers: response.headers
        });

        return response;
      } else {
        console.log('🌐 [HTTP] Using fetch API (web fallback)');

        // Fallback to fetch for web
        const fetchOptions: RequestInit = {
          method,
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            ...headers
          }
        };

        if (data) {
          fetchOptions.body = typeof data === 'string' ? data : JSON.stringify(data);
        }

        // Add params to URL if provided
        let requestUrl = url;
        if (params) {
          const urlParams = new URLSearchParams(params);
          requestUrl += (url.includes('?') ? '&' : '?') + urlParams.toString();
        }

        const response = await fetch(requestUrl, fetchOptions);

        const responseData = await response.text();
        let parsedData;
        try {
          parsedData = JSON.parse(responseData);
        } catch {
          parsedData = responseData;
        }

        console.log('✅ [HTTP] Fetch response received:', {
          status: response.status,
          statusText: response.statusText,
          ok: response.ok,
          hasData: !!responseData
        });

        return {
          status: response.status,
          statusText: response.statusText,
          data: parsedData,
          headers: Object.fromEntries(response.headers.entries()),
          url: response.url
        };
      }
    } catch (error) {
      console.error('❌ [HTTP] HTTP request failed:', error);
      throw error;
    }
  }

  /**
   * Make POST request with form data using official Capacitor HTTP
   */
  static async postForm(url: string, formData: Record<string, string>, headers: Record<string, string> = {}): Promise<HttpResponse> {
    // For the official Capacitor HTTP plugin, we can pass the form data directly
    // The plugin will handle the proper encoding
    return this.request({
      url,
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        ...headers
      },
      data: formData
    });
  }

  /**
   * Make POST request with JSON data using official Capacitor HTTP
   */
  static async postJson(url: string, jsonData: any, headers: Record<string, string> = {}): Promise<HttpResponse> {
    return this.request({
      url,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...headers
      },
      data: jsonData
    });
  }

  /**
   * Make GET request using official Capacitor HTTP
   */
  static async get(url: string, params?: Record<string, string>, headers: Record<string, string> = {}): Promise<HttpResponse> {
    return this.request({
      url,
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        ...headers
      },
      params
    });
  }

  /**
   * Test official Capacitor HTTP functionality
   */
  static async testHttp(): Promise<void> {
    console.log('🧪 [HTTP] Testing official Capacitor HTTP functionality...');
    console.log('🧪 [HTTP] Native HTTP available:', this.isNativeHttpAvailable());
    console.log('🧪 [HTTP] Platform:', Capacitor.getPlatform());

    try {
      // Test with a simple GET request to a public API
      const testUrl = 'https://httpbin.org/get';
      const response = await this.get(testUrl, { test: 'official-capacitor-http' });

      console.log('🧪 [HTTP] Test request successful:', {
        status: response.status,
        hasData: !!response.data
      });

      if (response.status === 200) {
        console.log('✅ [HTTP] Official Capacitor HTTP functionality test passed');
      } else {
        console.warn('⚠️ [HTTP] HTTP test returned non-200 status:', response.status);
      }
    } catch (error) {
      console.error('❌ [HTTP] Official Capacitor HTTP functionality test failed:', error);
      throw error;
    }
  }

  /**
   * Check if response indicates success
   */
  static isSuccessResponse(response: HttpResponse): boolean {
    return response.status >= 200 && response.status < 300;
  }

  /**
   * Extract error message from response
   */
  static getErrorMessage(response: HttpResponse): string {
    if (this.isSuccessResponse(response)) {
      return '';
    }

    try {
      if (typeof response.data === 'object' && response.data) {
        return response.data.error_description || response.data.error || response.data.message || `HTTP ${response.status}`;
      } else if (typeof response.data === 'string') {
        return response.data;
      }
    } catch (error) {
      console.warn('⚠️ [HTTP] Error parsing error message:', error);
    }

    return `HTTP ${response.status}: ${response.statusText || 'Unknown error'}`;
  }
}
