import {
  validateEmail,
  validatePassword,
  validateName,
  validatePasswordConfirmation,
  getPasswordStrength,
  getPasswordStrengthLabel,
  getPasswordStrengthColor,
} from '../validation';

describe('Validation Utils', () => {
  describe('validateEmail', () => {
    it('should validate correct email addresses', () => {
      const result = validateEmail('<EMAIL>');
      expect(result.isValid).toBe(true);
      expect(result.message).toBeUndefined();
    });

    it('should reject empty email', () => {
      const result = validateEmail('');
      expect(result.isValid).toBe(false);
      expect(result.message).toBe('El email es requerido');
    });

    it('should reject invalid email format', () => {
      const result = validateEmail('invalid-email');
      expect(result.isValid).toBe(false);
      expect(result.message).toBe('Ingresa un email válido');
    });
  });

  describe('validatePassword', () => {
    it('should validate strong password', () => {
      const result = validatePassword('StrongPass123');
      expect(result.isValid).toBe(true);
      expect(result.message).toBeUndefined();
    });

    it('should reject empty password', () => {
      const result = validatePassword('');
      expect(result.isValid).toBe(false);
      expect(result.message).toBe('La contraseña es requerida');
    });

    it('should reject short password', () => {
      const result = validatePassword('123');
      expect(result.isValid).toBe(false);
      expect(result.message).toBe('La contraseña debe tener al menos 6 caracteres');
    });

    it('should reject weak password', () => {
      const result = validatePassword('password');
      expect(result.isValid).toBe(false);
      expect(result.message).toBe('La contraseña debe contener al menos una mayúscula, una minúscula y un número');
    });
  });

  describe('validateName', () => {
    it('should validate correct name', () => {
      const result = validateName('John Doe');
      expect(result.isValid).toBe(true);
      expect(result.message).toBeUndefined();
    });

    it('should reject empty name', () => {
      const result = validateName('');
      expect(result.isValid).toBe(false);
      expect(result.message).toBe('El nombre es requerido');
    });

    it('should reject too short name', () => {
      const result = validateName('A');
      expect(result.isValid).toBe(false);
      expect(result.message).toBe('El nombre debe tener al menos 2 caracteres');
    });

    it('should reject too long name', () => {
      const result = validateName('A'.repeat(51));
      expect(result.isValid).toBe(false);
      expect(result.message).toBe('El nombre no puede exceder 50 caracteres');
    });
  });

  describe('validatePasswordConfirmation', () => {
    it('should validate matching passwords', () => {
      const result = validatePasswordConfirmation('password123', 'password123');
      expect(result.isValid).toBe(true);
      expect(result.message).toBeUndefined();
    });

    it('should reject empty confirmation', () => {
      const result = validatePasswordConfirmation('password123', '');
      expect(result.isValid).toBe(false);
      expect(result.message).toBe('Confirma tu contraseña');
    });

    it('should reject non-matching passwords', () => {
      const result = validatePasswordConfirmation('password123', 'different123');
      expect(result.isValid).toBe(false);
      expect(result.message).toBe('Las contraseñas no coinciden');
    });
  });

  describe('getPasswordStrength', () => {
    it('should return 0 for empty password', () => {
      expect(getPasswordStrength('')).toBe(0);
    });

    it('should return 1 for very weak password', () => {
      expect(getPasswordStrength('abc')).toBe(1);
    });

    it('should return 4 for strong password', () => {
      expect(getPasswordStrength('StrongPass123!')).toBe(4);
    });
  });

  describe('getPasswordStrengthLabel', () => {
    it('should return correct labels', () => {
      expect(getPasswordStrengthLabel(0)).toBe('Muy débil');
      expect(getPasswordStrengthLabel(1)).toBe('Muy débil');
      expect(getPasswordStrengthLabel(2)).toBe('Débil');
      expect(getPasswordStrengthLabel(3)).toBe('Buena');
      expect(getPasswordStrengthLabel(4)).toBe('Fuerte');
    });
  });

  describe('getPasswordStrengthColor', () => {
    it('should return correct colors', () => {
      expect(getPasswordStrengthColor(0)).toBe('danger');
      expect(getPasswordStrengthColor(1)).toBe('danger');
      expect(getPasswordStrengthColor(2)).toBe('warning');
      expect(getPasswordStrengthColor(3)).toBe('primary');
      expect(getPasswordStrengthColor(4)).toBe('success');
    });
  });
});
