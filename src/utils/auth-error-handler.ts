/**
 * Enhanced error handling for OIDC authentication
 * Provides specific error messages and debugging information
 */

import { debugLog } from '../config/environment.config';

export interface AuthError {
  code: string;
  message: string;
  userMessage: string;
  debugInfo?: any;
  isRetryable: boolean;
}

export class AuthErrorHandler {
  /**
   * Parse and categorize OIDC authentication errors
   */
  static parseError(error: any): AuthError {
    debugLog('AuthErrorHandler - Parsing error:', error);

    // Handle different types of errors
    if (error?.error) {
      return this.parseOIDCError(error);
    }

    if (error?.message) {
      return this.parseGenericError(error);
    }

    return {
      code: 'UNKNOWN_ERROR',
      message: 'Unknown authentication error',
      userMessage: 'Error desconocido durante la autenticación. Inténtalo de nuevo.',
      debugInfo: error,
      isRetryable: true
    };
  }

  /**
   * Parse OIDC-specific errors from the authorization server
   */
  private static parseOIDCError(error: any): AuthError {
    const errorCode = error.error;
    const errorDescription = error.error_description || '';

    switch (errorCode) {
      case 'invalid_request':
        return {
          code: 'INVALID_REQUEST',
          message: `Invalid request: ${errorDescription}`,
          userMessage: 'Solicitud de autenticación inválida. Verifica la configuración.',
          debugInfo: error,
          isRetryable: false
        };

      case 'unauthorized_client':
        return {
          code: 'UNAUTHORIZED_CLIENT',
          message: `Unauthorized client: ${errorDescription}`,
          userMessage: 'Cliente no autorizado. Verifica la configuración del cliente.',
          debugInfo: error,
          isRetryable: false
        };

      case 'access_denied':
        return {
          code: 'ACCESS_DENIED',
          message: `Access denied: ${errorDescription}`,
          userMessage: 'Acceso denegado. Verifica tus credenciales.',
          debugInfo: error,
          isRetryable: true
        };

      case 'unsupported_response_type':
        return {
          code: 'UNSUPPORTED_RESPONSE_TYPE',
          message: `Unsupported response type: ${errorDescription}`,
          userMessage: 'Tipo de respuesta no soportado. Contacta al administrador.',
          debugInfo: error,
          isRetryable: false
        };

      case 'invalid_scope':
        return {
          code: 'INVALID_SCOPE',
          message: `Invalid scope: ${errorDescription}`,
          userMessage: 'Permisos solicitados inválidos. Contacta al administrador.',
          debugInfo: error,
          isRetryable: false
        };

      case 'server_error':
        return {
          code: 'SERVER_ERROR',
          message: `Server error: ${errorDescription}`,
          userMessage: 'Error del servidor. Inténtalo más tarde.',
          debugInfo: error,
          isRetryable: true
        };

      case 'temporarily_unavailable':
        return {
          code: 'TEMPORARILY_UNAVAILABLE',
          message: `Service temporarily unavailable: ${errorDescription}`,
          userMessage: 'Servicio temporalmente no disponible. Inténtalo más tarde.',
          debugInfo: error,
          isRetryable: true
        };

      default:
        return {
          code: 'OIDC_ERROR',
          message: `OIDC error: ${errorCode} - ${errorDescription}`,
          userMessage: `Error de autenticación: ${errorDescription || errorCode}`,
          debugInfo: error,
          isRetryable: true
        };
    }
  }

  /**
   * Parse generic JavaScript errors
   */
  private static parseGenericError(error: any): AuthError {
    const message = error.message || error.toString();

    // Network errors
    if (message.includes('fetch') || message.includes('network') || message.includes('NetworkError')) {
      return {
        code: 'NETWORK_ERROR',
        message: `Network error: ${message}`,
        userMessage: 'Error de conexión. Verifica tu conexión a internet.',
        debugInfo: error,
        isRetryable: true
      };
    }

    // Timeout errors
    if (message.includes('timeout') || message.includes('Timeout')) {
      return {
        code: 'TIMEOUT_ERROR',
        message: `Timeout error: ${message}`,
        userMessage: 'La operación tardó demasiado. Inténtalo de nuevo.',
        debugInfo: error,
        isRetryable: true
      };
    }

    // State mismatch errors
    if (message.includes('state') || message.includes('State')) {
      return {
        code: 'STATE_MISMATCH',
        message: `State mismatch: ${message}`,
        userMessage: 'Error de estado de sesión. Inicia sesión nuevamente.',
        debugInfo: error,
        isRetryable: true
      };
    }

    // PKCE errors
    if (message.includes('code_verifier') || message.includes('code_challenge')) {
      return {
        code: 'PKCE_ERROR',
        message: `PKCE error: ${message}`,
        userMessage: 'Error de verificación de seguridad. Inténtalo de nuevo.',
        debugInfo: error,
        isRetryable: true
      };
    }

    // Browser/WebView errors
    if (message.includes('browser') || message.includes('webview')) {
      return {
        code: 'BROWSER_ERROR',
        message: `Browser error: ${message}`,
        userMessage: 'Error del navegador. Reinicia la aplicación.',
        debugInfo: error,
        isRetryable: true
      };
    }

    return {
      code: 'GENERIC_ERROR',
      message: `Generic error: ${message}`,
      userMessage: 'Error durante la autenticación. Inténtalo de nuevo.',
      debugInfo: error,
      isRetryable: true
    };
  }

  /**
   * Get debugging information for error reporting
   */
  static getDebugInfo(error: AuthError): string {
    return JSON.stringify({
      code: error.code,
      message: error.message,
      isRetryable: error.isRetryable,
      debugInfo: error.debugInfo,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }, null, 2);
  }

  /**
   * Check if error suggests a configuration issue
   */
  static isConfigurationError(error: AuthError): boolean {
    const configErrors = [
      'INVALID_REQUEST',
      'UNAUTHORIZED_CLIENT',
      'UNSUPPORTED_RESPONSE_TYPE',
      'INVALID_SCOPE'
    ];
    return configErrors.includes(error.code);
  }

  /**
   * Check if error is likely temporary and should be retried
   */
  static shouldRetry(error: AuthError): boolean {
    return error.isRetryable && !this.isConfigurationError(error);
  }
}
