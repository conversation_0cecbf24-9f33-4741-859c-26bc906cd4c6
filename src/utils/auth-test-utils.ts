/**
 * Authentication testing utilities
 * Provides functions to test and validate the OIDC authentication flow
 */

import { Capacitor } from '@capacitor/core';
import { userManager, debugLog } from '../config/user-manager.config';
import { ConfigValidator } from './config-validator';
import { AuthErrorHandler } from './auth-error-handler';
import { CapacitorAuthService } from '../services/capacitor-auth.service';

export interface TestResult {
  testName: string;
  passed: boolean;
  message: string;
  details?: any;
}

export class AuthTestUtils {
  /**
   * Run comprehensive authentication tests
   */
  static async runAuthenticationTests(): Promise<TestResult[]> {
    const results: TestResult[] = [];

    debugLog('AuthTestUtils - Starting authentication tests');

    // Test 1: Configuration validation
    results.push(await this.testConfiguration());

    // Test 2: UserManager initialization
    results.push(await this.testUserManagerInitialization());

    // Test 3: OIDC metadata retrieval
    results.push(await this.testOIDCMetadata());

    // Test 4: Authorization URL generation
    results.push(await this.testAuthorizationUrlGeneration());

    // Test 5: Platform-specific tests
    if (Capacitor.isNativePlatform()) {
      results.push(await this.testNativePlatformSetup());
    } else {
      results.push(await this.testWebPlatformSetup());
    }

    // Test 6: Error handling
    results.push(await this.testErrorHandling());

    debugLog('AuthTestUtils - Authentication tests completed', results);
    return results;
  }

  /**
   * Test configuration validation
   */
  private static async testConfiguration(): Promise<TestResult> {
    try {
      const validation = ConfigValidator.validateConfiguration();
      
      return {
        testName: 'Configuration Validation',
        passed: validation.isValid,
        message: validation.isValid 
          ? 'Configuration is valid' 
          : `Configuration errors: ${validation.errors.join(', ')}`,
        details: validation
      };
    } catch (error) {
      return {
        testName: 'Configuration Validation',
        passed: false,
        message: `Configuration test failed: ${error}`,
        details: error
      };
    }
  }

  /**
   * Test UserManager initialization
   */
  private static async testUserManagerInitialization(): Promise<TestResult> {
    try {
      const settings = await userManager.settings;
      
      const requiredSettings = [
        'authority',
        'client_id',
        'redirect_uri',
        'response_type',
        'scope'
      ];

      const missingSettings = requiredSettings.filter(setting => !settings[setting as keyof typeof settings]);

      if (missingSettings.length > 0) {
        return {
          testName: 'UserManager Initialization',
          passed: false,
          message: `Missing required settings: ${missingSettings.join(', ')}`,
          details: settings
        };
      }

      return {
        testName: 'UserManager Initialization',
        passed: true,
        message: 'UserManager initialized successfully',
        details: {
          authority: settings.authority,
          client_id: settings.client_id,
          redirect_uri: settings.redirect_uri,
          response_type: settings.response_type,
          scope: settings.scope
        }
      };
    } catch (error) {
      return {
        testName: 'UserManager Initialization',
        passed: false,
        message: `UserManager initialization failed: ${error}`,
        details: error
      };
    }
  }

  /**
   * Test OIDC metadata retrieval
   */
  private static async testOIDCMetadata(): Promise<TestResult> {
    try {
      const metadata = await userManager.metadataService.getMetadata();
      
      const requiredEndpoints = [
        'authorization_endpoint',
        'token_endpoint',
        'userinfo_endpoint'
      ];

      const missingEndpoints = requiredEndpoints.filter(endpoint => !metadata[endpoint as keyof typeof metadata]);

      if (missingEndpoints.length > 0) {
        return {
          testName: 'OIDC Metadata Retrieval',
          passed: false,
          message: `Missing required endpoints: ${missingEndpoints.join(', ')}`,
          details: metadata
        };
      }

      return {
        testName: 'OIDC Metadata Retrieval',
        passed: true,
        message: 'OIDC metadata retrieved successfully',
        details: {
          issuer: metadata.issuer,
          authorization_endpoint: metadata.authorization_endpoint,
          token_endpoint: metadata.token_endpoint,
          userinfo_endpoint: metadata.userinfo_endpoint,
          code_challenge_methods_supported: metadata.code_challenge_methods_supported
        }
      };
    } catch (error) {
      return {
        testName: 'OIDC Metadata Retrieval',
        passed: false,
        message: `OIDC metadata retrieval failed: ${error}`,
        details: error
      };
    }
  }

  /**
   * Test authorization URL generation
   */
  private static async testAuthorizationUrlGeneration(): Promise<TestResult> {
    try {
      // Test by manually constructing authorization URL using UserManager settings
      const settings = await userManager.settings;

      if (!settings.authority || !settings.client_id || !settings.redirect_uri) {
        return {
          testName: 'Authorization URL Generation',
          passed: false,
          message: 'Missing required settings for authorization URL generation',
          details: {
            authority: settings.authority,
            client_id: settings.client_id,
            redirect_uri: settings.redirect_uri
          }
        };
      }

      // Construct a test authorization URL
      const testState = 'test-state-' + Date.now();
      const testCodeChallenge = 'test-code-challenge';

      const authUrl = `${settings.authority}/connect/authorize?` +
        `client_id=${encodeURIComponent(settings.client_id!)}&` +
        `redirect_uri=${encodeURIComponent(settings.redirect_uri!)}&` +
        `response_type=code&` +
        `scope=${encodeURIComponent(settings.scope!)}&` +
        `state=${encodeURIComponent(testState)}&` +
        `code_challenge_method=S256&` +
        `code_challenge=${encodeURIComponent(testCodeChallenge)}`;

      // Validate the URL
      const url = new URL(authUrl);
      const requiredParams = ['client_id', 'redirect_uri', 'response_type', 'scope', 'state', 'code_challenge'];
      const missingParams = requiredParams.filter(param => !url.searchParams.get(param));

      if (missingParams.length > 0) {
        return {
          testName: 'Authorization URL Generation',
          passed: false,
          message: `Missing required parameters: ${missingParams.join(', ')}`,
          details: {
            url: authUrl,
            params: Object.fromEntries(url.searchParams.entries())
          }
        };
      }

      return {
        testName: 'Authorization URL Generation',
        passed: true,
        message: 'Authorization URL can be generated successfully',
        details: {
          url: authUrl,
          params: Object.fromEntries(url.searchParams.entries())
        }
      };
    } catch (error) {
      return {
        testName: 'Authorization URL Generation',
        passed: false,
        message: `Authorization URL generation failed: ${error}`,
        details: error
      };
    }
  }

  /**
   * Test native platform setup
   */
  private static async testNativePlatformSetup(): Promise<TestResult> {
    try {
      // Check if CapacitorAuthService is properly initialized
      const platform = Capacitor.getPlatform();

      // Test if we can create authorization URL for native platforms
      if (Capacitor.isNativePlatform()) {
        // Test state generation and storage
        const testState = 'test-state-' + Date.now();
        const testCodeVerifier = 'test-code-verifier-' + Date.now();
        const testNonce = 'test-nonce-' + Date.now();

        // This is a basic test - we can't fully test without actual authentication
        return {
          testName: 'Native Platform Setup',
          passed: true,
          message: `Native platform (${platform}) setup appears correct with enhanced OIDC support`,
          details: {
            platform: platform,
            isNative: Capacitor.isNativePlatform(),
            capacitorAuthServiceAvailable: !!CapacitorAuthService,
            canGenerateState: !!testState,
            canGenerateCodeVerifier: !!testCodeVerifier,
            canGenerateNonce: !!testNonce
          }
        };
      } else {
        return {
          testName: 'Native Platform Setup',
          passed: true,
          message: `Web platform setup - using standard UserManager flow`,
          details: {
            platform: platform,
            isNative: Capacitor.isNativePlatform(),
            capacitorAuthServiceAvailable: !!CapacitorAuthService
          }
        };
      }
    } catch (error) {
      return {
        testName: 'Native Platform Setup',
        passed: false,
        message: `Native platform setup failed: ${error}`,
        details: error
      };
    }
  }

  /**
   * Test web platform setup
   */
  private static async testWebPlatformSetup(): Promise<TestResult> {
    try {
      // Check if web environment is properly configured
      const isHttps = window.location.protocol === 'https:';
      const isLocalhost = window.location.hostname === 'localhost';
      
      if (!isHttps && !isLocalhost) {
        return {
          testName: 'Web Platform Setup',
          passed: false,
          message: 'Web platform should use HTTPS or localhost',
          details: {
            protocol: window.location.protocol,
            hostname: window.location.hostname,
            href: window.location.href
          }
        };
      }

      return {
        testName: 'Web Platform Setup',
        passed: true,
        message: 'Web platform setup is correct',
        details: {
          protocol: window.location.protocol,
          hostname: window.location.hostname,
          href: window.location.href,
          isHttps: isHttps,
          isLocalhost: isLocalhost
        }
      };
    } catch (error) {
      return {
        testName: 'Web Platform Setup',
        passed: false,
        message: `Web platform setup failed: ${error}`,
        details: error
      };
    }
  }

  /**
   * Test error handling
   */
  private static async testErrorHandling(): Promise<TestResult> {
    try {
      // Test error parsing with different error types
      const testErrors = [
        new Error('Network error'),
        { error: 'invalid_request', error_description: 'Test error' },
        { message: 'State mismatch error' }
      ];

      const parsedErrors = testErrors.map(error => AuthErrorHandler.parseError(error));
      
      const allParsed = parsedErrors.every(parsed => 
        parsed.code && parsed.message && parsed.userMessage !== undefined
      );

      if (!allParsed) {
        return {
          testName: 'Error Handling',
          passed: false,
          message: 'Error parsing failed for some error types',
          details: parsedErrors
        };
      }

      return {
        testName: 'Error Handling',
        passed: true,
        message: 'Error handling is working correctly',
        details: parsedErrors
      };
    } catch (error) {
      return {
        testName: 'Error Handling',
        passed: false,
        message: `Error handling test failed: ${error}`,
        details: error
      };
    }
  }

  /**
   * Generate test report
   */
  static generateTestReport(results: TestResult[]): string {
    const passed = results.filter(r => r.passed).length;
    const total = results.length;
    const failed = results.filter(r => !r.passed);

    let report = `# Authentication Test Report\n\n`;
    report += `**Summary:** ${passed}/${total} tests passed\n\n`;

    if (failed.length > 0) {
      report += `## Failed Tests\n\n`;
      failed.forEach(test => {
        report += `### ${test.testName}\n`;
        report += `**Error:** ${test.message}\n\n`;
      });
    }

    report += `## All Test Results\n\n`;
    results.forEach(test => {
      const status = test.passed ? '✅' : '❌';
      report += `${status} **${test.testName}:** ${test.message}\n\n`;
    });

    return report;
  }

  /**
   * Log test results to console
   */
  static logTestResults(results: TestResult[]): void {
    const passed = results.filter(r => r.passed).length;
    const total = results.length;

    console.group(`🧪 Authentication Tests (${passed}/${total} passed)`);
    
    results.forEach(test => {
      const emoji = test.passed ? '✅' : '❌';
      console.log(`${emoji} ${test.testName}: ${test.message}`);
      
      if (!test.passed && test.details) {
        console.error('Details:', test.details);
      }
    });
    
    console.groupEnd();
  }
}
