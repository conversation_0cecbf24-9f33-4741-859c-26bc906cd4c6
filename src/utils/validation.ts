/**
 * Form validation utilities for the agenda-familiar app
 */

export interface ValidationResult {
  isValid: boolean;
  message?: string;
}

/**
 * Validates email format
 */
export const validateEmail = (email: string): ValidationResult => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  if (!email) {
    return { isValid: false, message: 'El email es requerido' };
  }

  if (!emailRegex.test(email)) {
    return { isValid: false, message: 'Ingresa un email válido' };
  }

  return { isValid: true };
};

/**
 * Validates email or username format (for Santillana Connect OIDC)
 * Accepts both email addresses and usernames like "fami.pre"
 */
export const validateEmailOrUsername = (emailOrUsername: string): ValidationResult => {
  if (!emailOrUsername?.trim()) {
    return { isValid: false, message: 'El usuario o correo electrónico es requerido' };
  }

  const trimmedValue = emailOrUsername.trim();

  // Check if it's an email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  const isEmail = emailRegex.test(trimmedValue);

  // Check if it's a valid username format (alphanumeric, dots, underscores, hyphens)
  // This pattern allows usernames like "fami.pre", "user123", "test_user", etc.
  const usernameRegex = /^[a-zA-Z0-9._-]+$/;
  const isUsername = usernameRegex.test(trimmedValue);

  if (!isEmail && !isUsername) {
    return {
      isValid: false,
      message: 'Ingresa un usuario válido (ej: fami.pre) o correo electrónico'
    };
  }

  // Additional validation for username format
  if (isUsername && trimmedValue.length < 2) {
    return { isValid: false, message: 'El usuario debe tener al menos 2 caracteres' };
  }

  // Additional validation for username format - no consecutive dots
  if (isUsername && /\.{2,}/.test(trimmedValue)) {
    return { isValid: false, message: 'El usuario no puede contener puntos consecutivos' };
  }

  // Additional validation - cannot start or end with special characters
  if (isUsername && /^[._-]|[._-]$/.test(trimmedValue)) {
    return { isValid: false, message: 'El usuario no puede empezar o terminar con puntos, guiones o guiones bajos' };
  }

  return { isValid: true };
};

/**
 * Validates password strength
 */
export const validatePassword = (password: string): ValidationResult => {
  if (!password) {
    return { isValid: false, message: 'La contraseña es requerida' };
  }

  if (password.length < 6) {
    return { isValid: false, message: 'La contraseña debe tener al menos 6 caracteres' };
  }

  if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)) {
    return {
      isValid: false,
      message: 'La contraseña debe contener al menos una mayúscula, una minúscula y un número'
    };
  }

  return { isValid: true };
};

/**
 * Validates password for OIDC authentication (more lenient)
 * Since actual validation is done by Santillana Connect OIDC provider
 */
export const validatePasswordForOIDC = (password: string): ValidationResult => {
  if (!password?.trim()) {
    return { isValid: false, message: 'La contraseña es requerida' };
  }

  // More lenient validation for OIDC - just check minimum length
  if (password.trim().length < 1) {
    return { isValid: false, message: 'La contraseña es requerida' };
  }

  return { isValid: true };
};

/**
 * Validates name field
 */
export const validateName = (name: string): ValidationResult => {
  if (!name) {
    return { isValid: false, message: 'El nombre es requerido' };
  }
  
  if (name.length < 2) {
    return { isValid: false, message: 'El nombre debe tener al menos 2 caracteres' };
  }
  
  if (name.length > 50) {
    return { isValid: false, message: 'El nombre no puede exceder 50 caracteres' };
  }
  
  return { isValid: true };
};

/**
 * Validates password confirmation
 */
export const validatePasswordConfirmation = (
  password: string, 
  confirmPassword: string
): ValidationResult => {
  if (!confirmPassword) {
    return { isValid: false, message: 'Confirma tu contraseña' };
  }
  
  if (password !== confirmPassword) {
    return { isValid: false, message: 'Las contraseñas no coinciden' };
  }
  
  return { isValid: true };
};

/**
 * Gets password strength level (0-4)
 */
export const getPasswordStrength = (password: string): number => {
  let strength = 0;
  
  if (password.length >= 6) strength++;
  if (password.length >= 8) strength++;
  if (/[a-z]/.test(password)) strength++;
  if (/[A-Z]/.test(password)) strength++;
  if (/\d/.test(password)) strength++;
  if (/[^a-zA-Z\d]/.test(password)) strength++;
  
  return Math.min(strength, 4);
};

/**
 * Gets password strength label
 */
export const getPasswordStrengthLabel = (strength: number): string => {
  switch (strength) {
    case 0:
    case 1:
      return 'Muy débil';
    case 2:
      return 'Débil';
    case 3:
      return 'Buena';
    case 4:
      return 'Fuerte';
    default:
      return '';
  }
};

/**
 * Gets password strength color
 */
export const getPasswordStrengthColor = (strength: number): string => {
  switch (strength) {
    case 0:
    case 1:
      return 'danger';
    case 2:
      return 'warning';
    case 3:
      return 'primary';
    case 4:
      return 'success';
    default:
      return 'medium';
  }
};

/**
 * Validates a complete form object
 */
export const validateForm = (
  formData: Record<string, any>,
  validationRules: Record<string, (value: any) => ValidationResult>
): { isValid: boolean; errors: Record<string, string> } => {
  const errors: Record<string, string> = {};
  
  Object.keys(validationRules).forEach(field => {
    const result = validationRules[field](formData[field]);
    if (!result.isValid && result.message) {
      errors[field] = result.message;
    }
  });
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

/**
 * Debounce function for real-time validation
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};
