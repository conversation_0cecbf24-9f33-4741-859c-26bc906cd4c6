import { Capacitor } from "@capacitor/core";

// Environment detection
export const isNative = Capacitor.getPlatform() !== "web";
export const isDev = import.meta.env.DEV;
export const platform = Capacitor.getPlatform();

// Environment variables with fallbacks - matching GitLab implementation
export const environmentConfig = {
  // Santillana Connect OIDC Provider (Pre-production)
  authority: import.meta.env.VITE_OIDC_AUTHORITY || "https://pre-identity.santillanaconnect.com",
  clientId: import.meta.env.VITE_OIDC_CLIENT_ID || "sumun_office_co_pre",
  scope: import.meta.env.VITE_OIDC_SCOPE || "openid profile email offline_access neds/full_access",

  // Application URLs
  devBaseUrl: import.meta.env.VITE_DEV_BASE_URL || "https://localhost:5173",
  nativeBaseUrl: import.meta.env.VITE_NATIVE_BASE_URL || "capacitor://localhost",

  // Debug settings
  debugAuth: import.meta.env.VITE_DEBUG_AUTH === "true" || isDev
} as const;

// Base URLs configuration
export const getBaseUrl = (): string => {
  // If running as native app (iOS/Android via Capacitor)
  if (isNative) {
    return environmentConfig.nativeBaseUrl;
  }
  
  // If development mode (Docker/localhost web browser)
  if (isDev) {
    return environmentConfig.devBaseUrl;
  }
  
  // For production, use the current origin
  return window.location.origin;
};

export const baseUrl = getBaseUrl();

// Platform and configuration information for debugging
export const platformInfo = {
  isNative,
  isDev,
  platform,
  baseUrl,
  redirectUri: `${baseUrl}/callback`,
  logoutUri: `${baseUrl}/logout`,
  silentRefreshUri: `${baseUrl}/silent-refresh`,
  authority: environmentConfig.authority,
  clientId: environmentConfig.clientId,
  scope: environmentConfig.scope,
  debugEnabled: environmentConfig.debugAuth
};

// Debug logging helper
export const debugLog = (message: string, data?: any) => {
  if (environmentConfig.debugAuth) {
    console.log(`🔐 [AUTH] ${message}`, data || "");
  }
};

// Generate redirect URIs
export const redirectUris = {
  callback: `${baseUrl}/callback`,
  postLogout: `${baseUrl}/logout`,
  silentRefresh: `${baseUrl}/silent-refresh`,
};
