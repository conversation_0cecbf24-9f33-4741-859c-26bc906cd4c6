/**
 * Debug configuration for Chrome DevTools remote debugging
 * Ensures all console logs are properly forwarded when debugging Android via chrome://inspect
 */

import { Capacitor } from '@capacitor/core';

export class DebugConfig {
  private static initialized = false;

  /**
   * Initialize debug configuration for remote debugging
   */
  static initialize(): void {
    if (this.initialized) return;

    // Ensure console methods are available and properly forwarded
    this.setupConsoleForwarding();
    
    // Log initialization
    console.log('🔧 [DEBUG] Debug configuration initialized');
    console.log('🔧 [DEBUG] Platform:', Capacitor.getPlatform());
    console.log('🔧 [DEBUG] Is native platform:', Capacitor.isNativePlatform());
    console.log('🔧 [DEBUG] Available plugins:', this.getAvailablePlugins());

    this.initialized = true;
  }

  /**
   * Setup console forwarding for remote debugging
   */
  private static setupConsoleForwarding(): void {
    // Store original console methods
    const originalConsole = {
      log: console.log,
      error: console.error,
      warn: console.warn,
      info: console.info,
      debug: console.debug
    };

    // Enhanced console.log for remote debugging
    console.log = (...args: any[]) => {
      originalConsole.log(...args);
      // Ensure logs are flushed for remote debugging
      if (Capacitor.isNativePlatform()) {
        this.flushToRemoteDebugger('log', args);
      }
    };

    // Enhanced console.error for remote debugging
    console.error = (...args: any[]) => {
      originalConsole.error(...args);
      if (Capacitor.isNativePlatform()) {
        this.flushToRemoteDebugger('error', args);
      }
    };

    // Enhanced console.warn for remote debugging
    console.warn = (...args: any[]) => {
      originalConsole.warn(...args);
      if (Capacitor.isNativePlatform()) {
        this.flushToRemoteDebugger('warn', args);
      }
    };

    // Enhanced console.info for remote debugging
    console.info = (...args: any[]) => {
      originalConsole.info(...args);
      if (Capacitor.isNativePlatform()) {
        this.flushToRemoteDebugger('info', args);
      }
    };
  }

  /**
   * Flush logs to remote debugger
   */
  private static flushToRemoteDebugger(level: string, args: any[]): void {
    try {
      // Force flush by creating a temporary DOM element with the log data
      // This ensures logs are visible in Chrome DevTools remote debugging
      const logData = args.map(arg => 
        typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
      ).join(' ');

      // Create a hidden element to force log visibility in remote debugger
      const logElement = document.createElement('div');
      logElement.style.display = 'none';
      logElement.setAttribute('data-debug-log', `${level}: ${logData}`);
      document.body.appendChild(logElement);

      // Remove after a short delay to prevent DOM pollution
      setTimeout(() => {
        if (logElement.parentNode) {
          logElement.parentNode.removeChild(logElement);
        }
      }, 100);
    } catch (error) {
      // Silently fail to avoid infinite loops
    }
  }

  /**
   * Get list of available Capacitor plugins
   */
  private static getAvailablePlugins(): string[] {
    const plugins = [
      'App',
      'InAppBrowser',
      'Preferences',
      'Device',
      'Network',
      'StatusBar',
      'SplashScreen'
    ];

    return plugins.filter(plugin => Capacitor.isPluginAvailable(plugin));
  }

  /**
   * Log authentication flow start for debugging
   */
  static logAuthFlowStart(): void {
    console.log('🔐 [DEBUG] ==========================================');
    console.log('🔐 [DEBUG] AUTHENTICATION FLOW STARTING');
    console.log('🔐 [DEBUG] ==========================================');
    console.log('🔐 [DEBUG] Timestamp:', new Date().toISOString());
    console.log('🔐 [DEBUG] Platform:', Capacitor.getPlatform());
    console.log('🔐 [DEBUG] User Agent:', navigator.userAgent);
    console.log('🔐 [DEBUG] ==========================================');
  }

  /**
   * Log authentication flow end for debugging
   */
  static logAuthFlowEnd(success: boolean, result?: any): void {
    console.log('🔐 [DEBUG] ==========================================');
    console.log(`🔐 [DEBUG] AUTHENTICATION FLOW ${success ? 'COMPLETED' : 'FAILED'}`);
    console.log('🔐 [DEBUG] ==========================================');
    console.log('🔐 [DEBUG] Timestamp:', new Date().toISOString());
    if (result) {
      console.log('🔐 [DEBUG] Result:', result);
    }
    console.log('🔐 [DEBUG] ==========================================');
  }

  /**
   * Test console logging for remote debugging
   */
  static testConsoleLogging(): void {
    console.log('🧪 [TEST] Testing console.log - should appear in Chrome DevTools');
    console.error('🧪 [TEST] Testing console.error - should appear in Chrome DevTools');
    console.warn('🧪 [TEST] Testing console.warn - should appear in Chrome DevTools');
    console.info('🧪 [TEST] Testing console.info - should appear in Chrome DevTools');

    console.log('🧪 [TEST] Testing object logging:', {
      platform: Capacitor.getPlatform(),
      isNative: Capacitor.isNativePlatform(),
      timestamp: new Date().toISOString(),
      testData: {
        nested: true,
        array: [1, 2, 3],
        string: 'test'
      }
    });

    // Test crypto availability
    console.log('🧪 [TEST] Crypto availability:', {
      hasCrypto: !!(window.crypto),
      hasSubtle: !!(window.crypto && window.crypto.subtle),
      hasDigest: !!(window.crypto && window.crypto.subtle && window.crypto.subtle.digest),
      hasGetRandomValues: !!(window.crypto && window.crypto.getRandomValues)
    });

    // Test HTTP capabilities
    console.log('🧪 [TEST] HTTP capabilities:', {
      isNativePlatform: Capacitor.isNativePlatform(),
      hasOfficialCapacitorHttp: true, // Always available in Capacitor core
      hasFetch: !!(window.fetch)
    });
  }
}

// Auto-initialize when module is loaded
DebugConfig.initialize();
