import { AuthProviderProps } from "react-oidc-context";
import { WebStorageStateStore } from "oidc-client-ts";
import { environmentConfig, redirectUris, debugLog, platformInfo } from "./environment.config";

// OIDC Configuration for AuthProviderProps (simplified, without UserManager-specific properties)
export const oidcConfig: AuthProviderProps = {
  // Identity Provider configuration
  authority: environmentConfig.authority,
  client_id: environmentConfig.clientId,
  
  // OAuth2/OIDC flow configuration
  response_type: "code",
  scope: environmentConfig.scope,
  
  // Redirect URIs
  redirect_uri: redirectUris.callback,
  post_logout_redirect_uri: redirectUris.postLogout,
  silent_redirect_uri: redirectUris.silentRefresh,
  
  // Storage configuration
  userStore: new WebStorageStateStore({ 
    store: window.localStorage,
    prefix: "oidc."
  }),
  
  // Automatic token refresh
  automaticSilentRenew: true,
  includeIdTokenInSilentRenew: true,
  silentRequestTimeoutInSeconds: 30,
  
  // Security settings
  loadUserInfo: true,
  monitorSession: true,
  // Note: checkSessionInterval is not valid for AuthProviderProps - it's handled by UserManager
};

// Re-export shared configuration and utilities
export { platformInfo, debugLog };
