import { User<PERSON>anager, WebStorageStateStore, Log } from "oidc-client-ts";
import { Capacitor } from "@capacitor/core";
import { environmentConfig, redirectUris, debugLog, platformInfo } from "./environment.config";

// Enable debug logging if configured
if (environmentConfig.debugAuth) {
  Log.setLogger(console);
  Log.setLevel(Log.DEBUG);
}

// Create UserManager with PKCE configuration for Santillana Connect
export const userManager = new UserManager({
  // Santillana Connect Identity Provider
  authority: environmentConfig.authority,
  client_id: environmentConfig.clientId,

  // OAuth2/OIDC PKCE flow configuration
  response_type: "code", // Required for PKCE
  scope: environmentConfig.scope,
  response_mode: "query",

  // Redirect URIs for the application
  redirect_uri: redirectUris.callback,
  post_logout_redirect_uri: redirectUris.postLogout,
  silent_redirect_uri: redirectUris.silentRefresh,

  // Storage configuration for tokens and state
  userStore: new WebStorageStateStore({
    store: window.localStorage,
    prefix: "oidc.santillana.user."
  }),
  stateStore: new WebStorageStateStore({
    // Use localStorage for native platforms to persist during browser switch
    // Use sessionStorage for web to avoid persistence issues
    store: Capacitor.isNativePlatform() ? window.localStorage : window.sessionStorage,
    prefix: "oidc.santillana.state."
  }),

  // Automatic token refresh configuration
  automaticSilentRenew: true,
  includeIdTokenInSilentRenew: true,
  silentRequestTimeoutInSeconds: 30,

  // Security and session settings
  loadUserInfo: true,
  monitorSession: true,

  // PKCE will be automatically enabled by oidc-client-ts when:
  // 1. response_type is "code"
  // 2. No client_secret is provided (public client)
  // 3. The authorization server supports PKCE

  // Optional: Explicit metadata for Santillana Connect endpoints
  metadata: {
    issuer: environmentConfig.authority,
    authorization_endpoint: `${environmentConfig.authority}/connect/authorize`,
    token_endpoint: `${environmentConfig.authority}/connect/token`,
    userinfo_endpoint: `${environmentConfig.authority}/connect/userinfo`,
    end_session_endpoint: `${environmentConfig.authority}/connect/endsession`,
    jwks_uri: `${environmentConfig.authority}/.well-known/jwks`,

    // PKCE support - required for Santillana Connect
    code_challenge_methods_supported: ["S256"],
    response_types_supported: ["code"],
    grant_types_supported: ["authorization_code", "refresh_token"],

    // Santillana Connect supported features
    token_endpoint_auth_methods_supported: ["none"], // Public client
    response_modes_supported: ["query", "fragment"],

    // Additional security settings
    subject_types_supported: ["public"],
    id_token_signing_alg_values_supported: ["RS256"]
  },

  // Additional validation settings
  validateSubOnSilentRenew: true,
  clockSkewInSeconds: 300, // 5 minutes tolerance for clock skew

  // Error handling
  revokeTokensOnSignout: false, // Don't revoke tokens on signout for better UX
});

// PKCE will be automatically enabled by oidc-client-ts when:
// 1. response_type is "code"
// 2. The metadata specifies code_challenge_methods_supported
// 3. No client_secret is provided (public client)

// Configure session monitoring after UserManager creation
// This is the correct way to set session check interval
if (environmentConfig.debugAuth) {
  debugLog("UserManager configured with session monitoring enabled");
}

// Re-export shared configuration and utilities
export { platformInfo, debugLog };
