import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider, useTheme } from '../ThemeContext';

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock as any;

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Test component that uses the theme context
const TestComponent: React.FC = () => {
  const { isDarkMode, toggleTheme, setTheme } = useTheme();

  return (
    <div>
      <span data-testid="theme-status">
        {isDarkMode ? 'dark' : 'light'}
      </span>
      <button data-testid="toggle-button" onClick={toggleTheme}>
        Toggle
      </button>
      <button data-testid="set-dark-button" onClick={() => setTheme(true)}>
        Set Dark
      </button>
      <button data-testid="set-light-button" onClick={() => setTheme(false)}>
        Set Light
      </button>
    </div>
  );
};

describe('ThemeContext', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset document classes
    document.documentElement.className = '';
  });

  it('should provide default light theme when no saved preference', () => {
    localStorageMock.getItem.mockReturnValue(null);

    render(
      <ThemeProvider>
        <TestComponent />
      </ThemeProvider>
    );

    expect(screen.getByTestId('theme-status')).toHaveTextContent('light');
  });

  it('should load saved dark theme preference', () => {
    localStorageMock.getItem.mockReturnValue('dark');

    render(
      <ThemeProvider>
        <TestComponent />
      </ThemeProvider>
    );

    expect(screen.getByTestId('theme-status')).toHaveTextContent('dark');
  });

  it('should toggle theme when toggleTheme is called', () => {
    localStorageMock.getItem.mockReturnValue('light');

    render(
      <ThemeProvider>
        <TestComponent />
      </ThemeProvider>
    );

    expect(screen.getByTestId('theme-status')).toHaveTextContent('light');

    fireEvent.click(screen.getByTestId('toggle-button'));

    expect(screen.getByTestId('theme-status')).toHaveTextContent('dark');
  });

  it('should set specific theme when setTheme is called', () => {
    localStorageMock.getItem.mockReturnValue('light');

    render(
      <ThemeProvider>
        <TestComponent />
      </ThemeProvider>
    );

    expect(screen.getByTestId('theme-status')).toHaveTextContent('light');

    fireEvent.click(screen.getByTestId('set-dark-button'));

    expect(screen.getByTestId('theme-status')).toHaveTextContent('dark');

    fireEvent.click(screen.getByTestId('set-light-button'));

    expect(screen.getByTestId('theme-status')).toHaveTextContent('light');
  });

  it('should save theme preference to localStorage', () => {
    localStorageMock.getItem.mockReturnValue('light');

    render(
      <ThemeProvider>
        <TestComponent />
      </ThemeProvider>
    );

    fireEvent.click(screen.getByTestId('toggle-button'));

    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'agenda-familiar-theme',
      'dark'
    );
  });

  it('should throw error when useTheme is used outside provider', () => {
    // Suppress console.error for this test
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

    expect(() => {
      render(<TestComponent />);
    }).toThrow('useTheme must be used within a ThemeProvider');

    consoleSpy.mockRestore();
  });

  it('should apply ion-palette-dark class when dark mode is enabled', () => {
    localStorageMock.getItem.mockReturnValue('light');

    render(
      <ThemeProvider>
        <TestComponent />
      </ThemeProvider>
    );

    fireEvent.click(screen.getByTestId('set-dark-button'));

    // Wait for useEffect to run
    setTimeout(() => {
      expect(document.documentElement.classList.contains('ion-palette-dark')).toBe(true);
    }, 150);
  });

  it('should handle localStorage errors gracefully', () => {
    localStorageMock.getItem.mockImplementation(() => {
      throw new Error('localStorage error');
    });

    const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

    render(
      <ThemeProvider>
        <TestComponent />
      </ThemeProvider>
    );

    expect(screen.getByTestId('theme-status')).toHaveTextContent('light');
    expect(consoleSpy).toHaveBeenCalledWith(
      'Error reading theme preference from localStorage:',
      expect.any(Error)
    );

    consoleSpy.mockRestore();
  });
});
