import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';

/**
 * Theme Context for managing dark/light mode throughout the application
 * Following Ionic's official dark mode implementation guidelines
 */

interface ThemeContextType {
  isDarkMode: boolean;
  toggleTheme: () => void;
  setTheme: (isDark: boolean) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

const THEME_STORAGE_KEY = 'agenda-familiar-theme';

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  // Initialize theme from localStorage or system preference
  const [isDarkMode, setIsDarkMode] = useState<boolean>(() => {
    try {
      const savedTheme = localStorage.getItem(THEME_STORAGE_KEY);
      if (savedTheme !== null) {
        return savedTheme === 'dark';
      }
      // Fallback to system preference if no saved preference
      return window.matchMedia('(prefers-color-scheme: dark)').matches;
    } catch (error) {
      console.warn('Error reading theme preference from localStorage:', error);
      return false;
    }
  });

  // Apply theme changes to the DOM
  useEffect(() => {
    const applyTheme = (isDark: boolean) => {
      try {
        // Add transitioning class to prevent flash
        document.documentElement.classList.add('theme-transitioning');
        
        // Apply Ionic's official dark mode class
        document.documentElement.classList.toggle('ion-palette-dark', isDark);
        
        // Save preference to localStorage
        localStorage.setItem(THEME_STORAGE_KEY, isDark ? 'dark' : 'light');
        
        // Remove transitioning class after a brief delay
        setTimeout(() => {
          document.documentElement.classList.remove('theme-transitioning');
        }, 100);
      } catch (error) {
        console.warn('Error applying theme:', error);
      }
    };

    applyTheme(isDarkMode);
  }, [isDarkMode]);

  // Listen for system theme changes (only if user hasn't set a preference)
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleSystemThemeChange = (e: MediaQueryListEvent) => {
      try {
        const savedTheme = localStorage.getItem(THEME_STORAGE_KEY);
        // Only update if user hasn't manually set a preference
        if (savedTheme === null) {
          setIsDarkMode(e.matches);
        }
      } catch (error) {
        console.warn('Error handling system theme change:', error);
      }
    };

    // Add event listener for system theme changes
    mediaQuery.addEventListener('change', handleSystemThemeChange);
    
    // Cleanup
    return () => {
      mediaQuery.removeEventListener('change', handleSystemThemeChange);
    };
  }, []);

  const toggleTheme = () => {
    setIsDarkMode(prev => !prev);
  };

  const setTheme = (isDark: boolean) => {
    setIsDarkMode(isDark);
  };

  const value: ThemeContextType = {
    isDarkMode,
    toggleTheme,
    setTheme,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

/**
 * Custom hook to use theme context
 * Throws error if used outside of ThemeProvider
 */
export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export default ThemeContext;
