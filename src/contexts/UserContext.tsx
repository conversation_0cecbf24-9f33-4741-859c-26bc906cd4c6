import React, { createContext, useContext, useEffect, useState, useCallback, ReactNode } from 'react';
import { useAuth } from 'react-oidc-context';
import { AuthStorageService } from '../services/auth-storage.service';
import { UserApiService } from '../services/user-api.service';
import { debugLog, userManager } from '../config/user-manager.config';
import { clearOIDCStorage } from '../utils/oidc-debug';
import { Capacitor } from '@capacitor/core';
import { CapacitorAuthService } from '../services/capacitor-auth.service';

/**
 * User Context for managing authentication state and user information
 * throughout the application. Integrates with OIDC authentication system.
 */

export interface Student {
  id: string;
  name: string;
  grade: string;
  school: string;
  avatar?: string;
}

export interface User {
  id: string;
  name: string;
  email: string;
  phone?: string;
  role: string;
  avatar: string;
  memberSince: string;
  children: Student[];
}

interface UserContextType {
  user: User | null;
  isAuthenticated: boolean;
  selectedStudent: Student | null;
  isLoading: boolean;
  selectStudent: (student: Student) => void;
  updateUser: (userData: Partial<User>) => void;
  logout: () => Promise<void>;
  updateCapacitorAuthState?: (authResult?: any) => Promise<void>;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

const SELECTED_STUDENT_KEY = 'selected-student';

interface UserProviderProps {
  children: ReactNode;
}

// Helper function to create User object from OIDC profile using API service
const createUserFromOIDCProfile = async (oidcUser: any): Promise<User> => {
  const profile = oidcUser.profile || {};
  const accessToken = oidcUser.access_token;

  debugLog('UserContext - Creating user from OIDC profile with API integration', {
    sub: profile.sub,
    name: profile.name,
    email: profile.email,
    preferred_username: profile.preferred_username,
    hasAccessToken: !!accessToken,
    availableClaims: Object.keys(profile)
  });

  try {
    // Use UserApiService to fetch complete user profile from Santillana's backend
    const user = await UserApiService.fetchUserProfile(profile, accessToken);

    debugLog('UserContext - User profile created successfully from API', {
      userName: user.name,
      userEmail: user.email,
      childrenCount: user.children.length
    });

    return user;
  } catch (error) {
    console.warn('UserContext - Error fetching user profile from API, using fallback data:', error);

    // Fallback to basic profile data if API fails
    return {
      id: profile.sub || profile.id || 'oidc-user',
      name: profile.name || profile.preferred_username || profile.given_name || 'Usuario OIDC',
      email: profile.email || '<EMAIL>',
      phone: profile.phone_number || profile.phone || '+34 123 456 789',
      role: profile.role || profile.groups?.[0] || 'Padre/Madre',
      avatar: profile.picture || 'https://ionicframework.com/docs/img/demos/avatar.svg',
      memberSince: new Date().getFullYear().toString(),
      children: [] // Empty children array if API fails
    };
  }
};

// Helper function to load selected student from storage
const loadSelectedStudent = async (userData: User): Promise<Student | null> => {
  const savedStudent = localStorage.getItem(SELECTED_STUDENT_KEY);
  if (savedStudent) {
    try {
      const studentData = JSON.parse(savedStudent);
      // Verify the student still exists in the current user's children
      const studentExists = userData.children.find(child => child.id === studentData.id);
      if (studentExists) {
        return studentData;
      } else {
        debugLog('UserContext - Saved student no longer exists, using default');
        return userData.children[0] || null;
      }
    } catch (error) {
      debugLog('UserContext - Error parsing saved student, using default');
      return userData.children[0] || null;
    }
  } else if (userData.children.length > 0) {
    return userData.children[0];
  }
  return null;
};

export const UserProvider: React.FC<UserProviderProps> = ({ children }) => {
  const auth = useAuth();
  const [user, setUser] = useState<User | null>(null);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
  const [isInitialized, setIsInitialized] = useState<boolean>(false);
  const [isCapacitorAuthenticated, setIsCapacitorAuthenticated] = useState<boolean>(false);

  // Initialize Capacitor auth service for native platforms
  useEffect(() => {
    if (Capacitor.isNativePlatform()) {
      debugLog('UserContext - Initializing Capacitor auth service for native platform');
      CapacitorAuthService.initialize().catch(error => {
        console.error('UserContext - Error initializing Capacitor auth service:', error);
      });
    }
  }, []);

  // Initialize user data from OIDC auth state (web and native)
  useEffect(() => {
    const initializeUser = async () => {
      try {
        const isNative = Capacitor.isNativePlatform();

        debugLog('UserContext - Initializing user authentication', {
          isNative,
          isOIDCAuthenticated: auth.isAuthenticated,
          isOIDCLoading: auth.isLoading,
          hasOIDCUser: !!auth.user,
          userEmail: auth.user?.profile?.email
        });

        // For native platforms, also check Capacitor auth service
        if (isNative) {
          const capacitorUser = await CapacitorAuthService.getCurrentUser();
          if (capacitorUser && capacitorUser.expiresIn > 0) {
            debugLog('UserContext - Found valid user in Capacitor auth service');

            // Create user object from Capacitor user data
            const userData = await createUserFromOIDCProfile(capacitorUser);
            setUser(userData);
            setIsCapacitorAuthenticated(true);

            // Store tokens for native apps
            await AuthStorageService.storeTokens(capacitorUser);

            // Load selected student
            const student = await loadSelectedStudent(userData);
            setSelectedStudent(student);

            debugLog('UserContext - Capacitor user initialized successfully', {
              userName: userData.name,
              userEmail: userData.email,
              childrenCount: userData.children.length,
              isAuthenticated: true
            });

            setIsInitialized(true);
            return;
          } else {
            debugLog('UserContext - No valid Capacitor user found');
            setIsCapacitorAuthenticated(false);
          }
        }

        // Standard OIDC authenticated user (web or native fallback)
        if (auth.isAuthenticated && auth.user) {
          debugLog('UserContext - Processing OIDC authenticated user');

          // Create user object from OIDC profile with real API data
          const userData = await createUserFromOIDCProfile(auth.user);
          setUser(userData);

          // Store tokens for native apps
          await AuthStorageService.storeTokens(auth.user);

          // Load selected student
          const student = await loadSelectedStudent(userData);
          setSelectedStudent(student);

          debugLog('UserContext - OIDC user initialized successfully', {
            userName: userData.name,
            userEmail: userData.email,
            childrenCount: userData.children.length
          });
        }
        // No authentication - clear everything
        else if (!auth.isLoading) {
          debugLog('UserContext - No authentication found, clearing user data');
          setUser(null);
          setSelectedStudent(null);
          setIsCapacitorAuthenticated(false);
          localStorage.removeItem(SELECTED_STUDENT_KEY);
        }
      } catch (error) {
        console.warn('UserContext - Error initializing user data:', error);
        setUser(null);
        setSelectedStudent(null);
        localStorage.removeItem('isTestUser');
      } finally {
        if (!auth.isLoading) {
          setIsInitialized(true);
        }
      }
    };

    initializeUser();
  }, [auth.isAuthenticated, auth.isLoading, auth.user]);

  // Save selected student to localStorage when it changes
  useEffect(() => {
    if (selectedStudent) {
      try {
        localStorage.setItem(SELECTED_STUDENT_KEY, JSON.stringify(selectedStudent));
        debugLog('UserContext - Selected student saved', selectedStudent.name);
      } catch (error) {
        console.warn('UserContext - Error saving selected student:', error);
      }
    }
  }, [selectedStudent]);

  const logout = async () => {
    try {
      debugLog('UserContext - Initiating local logout (no external redirect)');

      // Clear local React state immediately
      setUser(null);
      setSelectedStudent(null);

      // Clear all stored data
      localStorage.removeItem(SELECTED_STUDENT_KEY);
      await AuthStorageService.clearTokens();

      // Clear OIDC storage (state, user data, etc.)
      clearOIDCStorage();

      // Handle platform-specific logout
      if (Capacitor.isNativePlatform()) {
        // For native platforms, use Capacitor auth service
        try {
          await CapacitorAuthService.signOut();
          debugLog('UserContext - Capacitor logout completed');
        } catch (capacitorError) {
          debugLog('UserContext - Error with Capacitor logout (continuing):', capacitorError);
        }
      } else {
        // For web platforms, remove user from OIDC context without external redirect
        try {
          await auth.removeUser();
          debugLog('UserContext - OIDC user removed from context');
        } catch (oidcError) {
          debugLog('UserContext - Error removing OIDC user (continuing with logout):', oidcError);
        }
      }

      // Clear any remaining OIDC data from UserManager
      try {
        await userManager.removeUser();
        debugLog('UserContext - User removed from UserManager');
      } catch (managerError) {
        debugLog('UserContext - Error removing user from UserManager (continuing):', managerError);
      }

      debugLog('UserContext - Local logout completed successfully');

      // Redirect to auth page immediately without external webview
      setTimeout(() => {
        window.location.href = '/auth';
      }, 100); // Small delay to ensure state is cleared

    } catch (error) {
      console.error('UserContext - Error during local logout:', error);

      // Force clear everything even if there are errors
      setUser(null);
      setSelectedStudent(null);
      localStorage.removeItem(SELECTED_STUDENT_KEY);

      try {
        await AuthStorageService.clearTokens();
        clearOIDCStorage();
      } catch (clearError) {
        debugLog('UserContext - Error clearing storage during error recovery:', clearError);
      }

      // Always redirect to auth page, never to external logout
      window.location.href = '/auth';
    }
  };

  const selectStudent = (student: Student) => {
    setSelectedStudent(student);
    debugLog('UserContext - Student selected', student.name);
  };

  const updateUser = (userData: Partial<User>) => {
    if (user) {
      const updatedUser = { ...user, ...userData };
      setUser(updatedUser);
      debugLog('UserContext - User updated', updatedUser.name);
    }
  };

  // Method to update Capacitor authentication state
  const updateCapacitorAuthState = useCallback(async (authResult?: any) => {
    if (Capacitor.isNativePlatform()) {
      if (authResult) {
        debugLog('UserContext - Updating Capacitor auth state with new user');

        // Create user object from auth result
        const userData = await createUserFromOIDCProfile(authResult);
        setUser(userData);
        setIsCapacitorAuthenticated(true);

        // Load selected student
        const student = await loadSelectedStudent(userData);
        setSelectedStudent(student);

        debugLog('UserContext - Capacitor auth state updated successfully', {
          userName: userData.name,
          userEmail: userData.email,
          isAuthenticated: true
        });
      } else {
        debugLog('UserContext - Clearing Capacitor auth state');
        setUser(null);
        setSelectedStudent(null);
        setIsCapacitorAuthenticated(false);
      }
    }
  }, []);

  // Determine authentication status based on platform
  const isAuthenticated = Capacitor.isNativePlatform()
    ? isCapacitorAuthenticated
    : auth.isAuthenticated;

  const value: UserContextType = {
    user,
    isAuthenticated,
    selectedStudent,
    isLoading: auth.isLoading || !isInitialized,
    logout,
    selectStudent,
    updateUser,
    updateCapacitorAuthState,
  };

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  );
};

/**
 * Custom hook to use user context
 * Throws error if used outside of UserProvider
 */
export const useUser = (): UserContextType => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};

export default UserContext;
