PODS:
  - Capacitor (7.2.0):
    - Capac<PERSON><PERSON>ordova
  - CapacitorApp (7.0.1):
    - Capacitor
  - CapacitorBrowser (7.0.1):
    - Capacitor
  - CapacitorCordova (7.2.0)
  - CapacitorHaptics (7.0.1):
    - Capacitor
  - CapacitorInappbrowser (2.1.1):
    - Capacitor
    - OSInAppBrowserLib (~> 2.0.1)
  - CapacitorKeyboard (7.0.1):
    - Capacitor
  - CapacitorPreferences (7.0.1):
    - Capacitor
  - CapacitorStatusBar (7.0.1):
    - Capacitor
  - OSInAppBrowserLib (2.0.1)

DEPENDENCIES:
  - "Capacitor (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorApp (from `../../node_modules/@capacitor/app`)"
  - "CapacitorBrowser (from `../../node_modules/@capacitor/browser`)"
  - "CapacitorCordova (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorHaptics (from `../../node_modules/@capacitor/haptics`)"
  - "CapacitorInappbrowser (from `../../node_modules/@capacitor/inappbrowser`)"
  - "CapacitorKeyboard (from `../../node_modules/@capacitor/keyboard`)"
  - "CapacitorPreferences (from `../../node_modules/@capacitor/preferences`)"
  - "CapacitorStatusBar (from `../../node_modules/@capacitor/status-bar`)"

SPEC REPOS:
  trunk:
    - OSInAppBrowserLib

EXTERNAL SOURCES:
  Capacitor:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorApp:
    :path: "../../node_modules/@capacitor/app"
  CapacitorBrowser:
    :path: "../../node_modules/@capacitor/browser"
  CapacitorCordova:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorHaptics:
    :path: "../../node_modules/@capacitor/haptics"
  CapacitorInappbrowser:
    :path: "../../node_modules/@capacitor/inappbrowser"
  CapacitorKeyboard:
    :path: "../../node_modules/@capacitor/keyboard"
  CapacitorPreferences:
    :path: "../../node_modules/@capacitor/preferences"
  CapacitorStatusBar:
    :path: "../../node_modules/@capacitor/status-bar"

SPEC CHECKSUMS:
  Capacitor: 03bc7cbdde6a629a8b910a9d7d78c3cc7ed09ea7
  CapacitorApp: febecbb9582cb353aed037e18ec765141f880fe9
  CapacitorBrowser: 6299776d496e968505464884d565992faa20444a
  CapacitorCordova: 5967b9ba03915ef1d585469d6e31f31dc49be96f
  CapacitorHaptics: 1f1e17041f435d8ead9ff2a34edd592c6aa6a8d6
  CapacitorInappbrowser: 614dedc76631d29280e60983afe2cd3472c3c1ed
  CapacitorKeyboard: 09fd91dcde4f8a37313e7f11bde553ad1ed52036
  CapacitorPreferences: 6c98117d4d7508034a4af9db64d6b26fc75d7b94
  CapacitorStatusBar: 6e7af040d8fc4dd655999819625cae9c2d74c36f
  OSInAppBrowserLib: cf8bfbe5d611a423a4153d8eb6ffbd92556dddb1

PODFILE CHECKSUM: 87398cec9a22b8033c0b3b0bb9696b1496d8dbf9

COCOAPODS: 1.16.2
