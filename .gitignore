# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/dist

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
/.nx
/.nx/cache
/.vscode/*
!/.vscode/extensions.json
.vscode
.idea

npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Optional eslint cache
.eslintcache
