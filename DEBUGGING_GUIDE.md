# 🔍 Authentication Debugging Guide

## 🚨 **Current Issue Analysis**

Based on your logs, the authentication is **actually working** but there are UI/UX issues:

1. ✅ **Tokens are being stored successfully** (seen in logs)
2. ✅ **User profile is retrieved** (FAMILIAR COMPARTIR, <EMAIL>)
3. ❌ **WebView closes immediately** (user doesn't see login form)
4. ❌ **UI stuck in loading state** (promise not resolving properly)
5. ⚠️ **Async listener warning** (using addListener without await)

## 🛠️ **Fixes Applied**

### **1. Enhanced Logging System**
- Added comprehensive console logging with emojis for easy identification
- All authentication steps now log to browser console
- Debug helper function added for state inspection

### **2. Fixed Async Listener Warning**
- Added `await` to `App.addListener()` calls
- Proper cleanup of listeners

### **3. Enhanced UI State Management**
- Better loading state handling
- Proper error display and auto-hide
- Success message with user name
- Extended navigation delay for better UX

### **4. Debug Tools Added**
- New debug page at `/debug` route
- Authentication state inspection
- Token and storage management tools

## 🧪 **Testing Instructions**

### **Step 1: Clear All Data (Important!)**
```bash
# Open browser developer tools and run:
await Preferences.clear();
localStorage.clear();
sessionStorage.clear();
```

### **Step 2: Access Debug Page**
Navigate to: `http://localhost:5173/debug` (or your app URL + `/debug`)

### **Step 3: Run Debug Tests**
1. **Check Platform Info** - Verify Capacitor setup
2. **Check Auth State** - See current authentication status
3. **Check Stored Keys** - See what's in storage
4. **Test Sign In** - Try the authentication flow
5. **Check Current User** - Verify user retrieval

### **Step 4: Monitor Console Logs**
Open browser developer tools and watch for these logs during authentication:

```
🔐 [AUTH] Initializing modern WebView authentication service
🚀 [AUTH] Starting secure WebView authentication
👤 [AUTH] User already authenticated, returning existing session (if cached)
🔑 [AUTH] Generating authentication state and PKCE parameters
💾 [AUTH] Storing authentication state securely
🔗 [AUTH] Building authorization URL
🌐 [AUTH] Opening secure WebView with URL: https://...
✅ [AUTH] WebView opened successfully, waiting for callback
🔗 [AUTH] Deep link received: capacitor://localhost/callback?code=...
🔄 [AUTH] Processing authentication callback
🔒 [AUTH] Valid OIDC callback detected, processing...
🚪 [AUTH] Closing WebView
🔍 [AUTH] Parsing callback URL parameters
🎫 [AUTH] Authorization code received, exchanging for tokens
🎉 [AUTH] Authentication completed successfully
💾 [AUTH] Storing authentication result securely
✅ [AUTH] Resolving authentication promise
🧹 [AUTH] Cleaning up authentication state
```

### **Step 5: Check for Issues**

#### **If WebView Closes Immediately:**
```javascript
// Check if user is already authenticated
await CapacitorAuthService.getCurrentUser();
// If this returns a user, that's why WebView closes - user is already logged in
```

#### **If UI Stays in Loading State:**
```javascript
// Check authentication state
await CapacitorAuthService.debugAuthState();
// Look for "Has promise resolver: true/false"
```

#### **If No Deep Link Received:**
- Check AndroidManifest.xml configuration
- Verify deep link intent filter is correct
- Test deep link manually: `adb shell am start -W -a android.intent.action.VIEW -d "capacitor://localhost/callback?code=test" com.santillana.agendafamiliar`

## 🔧 **Manual Testing Commands**

### **In Browser Console:**
```javascript
// Check current authentication state
await CapacitorAuthService.debugAuthState();

// Clear all authentication data
await CapacitorAuthService.signOut();
await Preferences.clear();

// Test authentication flow
try {
  const result = await CapacitorAuthService.signIn();
  console.log('Auth successful:', result);
} catch (error) {
  console.error('Auth failed:', error);
}

// Check stored tokens
const tokens = await Preferences.get({ key: 'auth_tokens' });
console.log('Stored tokens:', JSON.parse(tokens.value || '{}'));
```

### **In Android Logcat:**
```bash
# Filter for Capacitor logs
adb logcat | grep -E "(Capacitor|AUTH)"

# Filter for specific authentication logs
adb logcat | grep -E "🔐|🚀|🎉|❌"
```

## 🎯 **Expected Behavior**

### **Scenario 1: First Time Login**
1. User clicks "Iniciar Sesión"
2. WebView opens with Santillana Connect login form
3. User enters credentials
4. WebView redirects to callback URL
5. Deep link triggers callback processing
6. Tokens are exchanged and stored
7. Success message shows with user name
8. App navigates to home page

### **Scenario 2: Already Authenticated**
1. User clicks "Iniciar Sesión"
2. System detects existing valid tokens
3. WebView may open briefly but closes immediately
4. Success message shows with existing user name
5. App navigates to home page

## 🚨 **Common Issues & Solutions**

### **Issue: "Authentication already in progress"**
```javascript
// Reset authentication state
CapacitorAuthService.cleanup();
```

### **Issue: "State mismatch"**
```javascript
// Clear stored auth states
const keys = await Preferences.keys();
for (const key of keys.keys) {
  if (key.startsWith('auth_state_')) {
    await Preferences.remove({ key });
  }
}
```

### **Issue: "Token exchange failed"**
- Check network connectivity
- Verify OIDC configuration in environment.config.ts
- Check if Santillana Connect is accessible

### **Issue: WebView doesn't open**
```javascript
// Check if InAppBrowser is available
console.log('InAppBrowser available:', Capacitor.isPluginAvailable('InAppBrowser'));
```

## 📱 **Platform-Specific Testing**

### **Android Testing:**
```bash
# Build and run
npx cap build android
npx cap sync android
npx cap run android

# Check logs
adb logcat | grep Capacitor
```

### **iOS Testing:**
```bash
# Build and run
npx cap build ios
npx cap sync ios
npx cap run ios

# Check logs in Xcode console
```

### **Web Testing:**
```bash
# Run in browser (fallback to standard OIDC)
npm run dev
```

## 🎯 **Success Indicators**

✅ **Authentication Working:**
- Console shows complete authentication flow logs
- User profile data is retrieved and displayed
- Tokens are stored in Preferences
- App navigates to home page
- No error messages in console

✅ **UI Working:**
- Loading spinner shows during authentication
- Success message displays with user name
- Loading state clears after authentication
- Navigation happens smoothly

This debugging guide should help you identify exactly where the authentication flow is failing and provide the tools to fix it!
