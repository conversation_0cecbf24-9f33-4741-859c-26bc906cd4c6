# OIDC PKCE Login Testing Guide

This guide provides comprehensive testing procedures for the Santillana Connect OIDC PKCE authentication implementation.

## Pre-Testing Checklist

### 1. Environment Configuration
- [ ] Verify `VITE_OIDC_AUTHORITY` is set to `https://pre-identity.santillanaconnect.com`
- [ ] Verify `VITE_OIDC_CLIENT_ID` is set to `sumun_office_co_pre`
- [ ] Verify `VITE_OIDC_SCOPE` includes required scopes
- [ ] Verify `VITE_DEBUG_AUTH=true` for testing

### 2. Android Configuration
- [ ] AndroidManifest.xml includes deep link intent filter for `capacitor://localhost`
- [ ] MainActivity has `android:launchMode="singleTask"`
- [ ] App permissions include `INTERNET`

### 3. Redirect URIs
- [ ] Web: `https://localhost:5173/callback`
- [ ] Web: `https://localhost:5173/silent-refresh`
- [ ] Native: `capacitor://localhost/callback`

## Testing Scenarios

### Scenario 1: Web Browser Authentication (Development)

**Prerequisites:**
- Development server running on `https://localhost:5173`
- Valid SSL certificate for localhost

**Steps:**
1. Open browser to `https://localhost:5173`
2. Navigate to login page
3. Click "Iniciar Sesión con Santillana Connect"
4. Verify redirect to Santillana Connect
5. Enter valid credentials
6. Verify redirect back to app
7. Verify successful authentication and token storage

**Expected Results:**
- No console errors
- Successful redirect flow
- Tokens stored in localStorage
- User profile information available

### Scenario 2: Android Emulator Authentication

**Prerequisites:**
- Android emulator running
- App installed via `npx cap run android`
- Emulator has internet connectivity

**Steps:**
1. Launch app on Android emulator
2. Navigate to login page
3. Click "Iniciar Sesión con Santillana Connect"
4. Verify external browser opens
5. Enter valid credentials
6. Verify app returns to foreground
7. Verify successful authentication

**Expected Results:**
- No app crashes
- Browser opens correctly
- Deep link callback works
- App resumes properly
- Tokens stored in Capacitor Preferences

### Scenario 3: Error Handling Tests

**Test Invalid Credentials:**
1. Attempt login with invalid credentials
2. Verify appropriate error message
3. Verify app doesn't crash

**Test Network Issues:**
1. Disable network during authentication
2. Verify timeout handling
3. Verify retry mechanism

**Test Configuration Errors:**
1. Temporarily modify client ID
2. Verify "invalid_client" error handling
3. Restore correct configuration

## Debugging Tools

### 1. Configuration Validator
The app automatically validates configuration on startup. Check console for:
- ✅ Configuration validation passed
- ❌ Configuration validation failed
- ⚠️ Configuration warnings

### 2. Debug Logging
Enable debug logging with `VITE_DEBUG_AUTH=true`. Look for:
```
🔐 [AUTH] UserManager configured with session monitoring enabled
🔐 [AUTH] CapacitorAuthService - Starting native OIDC authentication
🔐 [AUTH] AuthPage - Using Capacitor native authentication flow
```

### 3. Error Boundary
Authentication errors are caught by `AuthErrorBoundary` which provides:
- User-friendly error messages
- Recovery options (retry, reload, go home)
- Debug information in development mode

## Common Issues and Solutions

### Issue: "Invalid Request" Error

**Possible Causes:**
- Incorrect redirect URI configuration
- Missing or invalid PKCE parameters
- Client ID mismatch

**Solutions:**
1. Verify redirect URIs match Santillana Connect configuration
2. Check client ID is correct
3. Ensure PKCE is properly implemented

### Issue: Android Emulator Crashes

**Possible Causes:**
- Missing deep link configuration
- Memory issues
- WebView problems

**Solutions:**
1. Verify AndroidManifest.xml deep link configuration
2. Use `android:launchMode="singleTask"`
3. Test on different emulator versions
4. Check for memory leaks

### Issue: State Mismatch Errors

**Possible Causes:**
- Session storage issues
- Multiple authentication attempts
- Browser/app state conflicts

**Solutions:**
1. Clear browser/app storage
2. Ensure single authentication flow
3. Check state parameter handling

## Performance Testing

### Load Testing
1. Perform multiple login/logout cycles
2. Monitor memory usage
3. Check for memory leaks

### Network Testing
1. Test on slow networks
2. Test with intermittent connectivity
3. Verify timeout handling

## Security Testing

### PKCE Validation
1. Verify code_challenge is generated
2. Verify code_verifier is stored securely
3. Verify proper code exchange

### Token Security
1. Verify tokens are stored securely
2. Check token expiration handling
3. Verify refresh token flow

## Automated Testing Commands

```bash
# Run configuration validation
npm run dev
# Check console for validation results

# Build and test Android
npx cap build android
npx cap run android

# Test web version
npm run dev
# Open https://localhost:5173

# Check logs
npx cap run android --livereload --external
# Monitor console output
```

## Troubleshooting Checklist

When authentication fails:

1. **Check Configuration:**
   - [ ] Authority URL is correct
   - [ ] Client ID matches Santillana Connect
   - [ ] Redirect URIs are registered

2. **Check Network:**
   - [ ] Internet connectivity
   - [ ] Firewall/proxy settings
   - [ ] SSL certificate validity

3. **Check Platform:**
   - [ ] Android: Deep link configuration
   - [ ] iOS: URL scheme configuration
   - [ ] Web: HTTPS setup

4. **Check Logs:**
   - [ ] Browser console errors
   - [ ] Android logcat output
   - [ ] Network request/response

5. **Check Storage:**
   - [ ] Clear browser storage
   - [ ] Clear app data
   - [ ] Verify token storage

## Success Criteria

Authentication is considered successful when:
- [ ] User can log in without crashes
- [ ] Tokens are properly stored
- [ ] User profile is available
- [ ] Silent refresh works
- [ ] Logout clears session
- [ ] Error handling works properly
- [ ] Performance is acceptable

## Reporting Issues

When reporting authentication issues, include:
1. Platform (web/Android/iOS)
2. Environment (dev/staging/prod)
3. Error messages and stack traces
4. Configuration validation results
5. Network logs
6. Steps to reproduce
7. Expected vs actual behavior
