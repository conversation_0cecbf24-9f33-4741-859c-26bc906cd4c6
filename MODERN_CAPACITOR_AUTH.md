# Modern Capacitor Authentication System

## Overview

This document describes the completely redesigned authentication system for the Santillana Connect integration using modern Capacitor best practices and secure WebView authentication.

## Problem Solved

The previous implementation failed with "UserManager state store not available" and "Cannot assign to read only property 'assign'" errors. The new system eliminates these issues by:

1. **Removing dependency on oidc-client-ts UserManager** for native platforms
2. **Using Capacitor InAppBrowser** for secure WebView authentication
3. **Implementing proper PKCE flow** with SHA256 code challenges
4. **Using Capacitor Preferences** for secure token storage
5. **Providing seamless cross-platform compatibility**

## Architecture

### Core Components

1. **CapacitorAuthService**: Modern authentication service using Capacitor APIs
2. **InAppBrowser**: Secure WebView for authentication flow
3. **Preferences**: Secure native storage for tokens and state
4. **Deep Link Handling**: Proper callback processing

### Authentication Flow

```mermaid
sequenceDiagram
    participant App as Mobile App
    participant Auth as CapacitorAuthService
    participant WebView as InAppBrowser
    participant SC as Santillana Connect
    participant Storage as Preferences

    App->>Auth: signIn()
    Auth->>Auth: generateAuthState()
    Auth->>Storage: storeAuthState()
    Auth->>Auth: buildAuthorizationUrl()
    Auth->>WebView: openInSystemBrowser()
    WebView->>SC: Navigate to auth URL
    SC->>SC: User enters credentials
    SC->>App: Redirect to capacitor://localhost/callback
    App->>Auth: handleAuthCallback()
    Auth->>Auth: processAuthCallback()
    Auth->>SC: Exchange code for tokens
    SC->>Auth: Return tokens
    Auth->>Storage: storeAuthResult()
    Auth->>App: Return AuthResult
```

## Key Features

### 1. **Secure WebView Authentication**
```typescript
await InAppBrowser.openInSystemBrowser({
  url: authUrl,
  options: {
    android: {
      showTitle: true,
      hideToolbarOnScroll: false,
      viewStyle: 'FULL_SCREEN'
    },
    iOS: {
      closeButtonText: 'Cancel',
      viewStyle: 'FULL_SCREEN',
      enableBarsCollapsing: false
    }
  }
});
```

### 2. **Proper PKCE Implementation**
```typescript
private static async generateCodeChallenge(codeVerifier: string): Promise<string> {
  const encoder = new TextEncoder();
  const data = encoder.encode(codeVerifier);
  const digest = await crypto.subtle.digest('SHA-256', data);
  
  const base64String = btoa(String.fromCharCode(...new Uint8Array(digest)));
  return base64String.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
}
```

### 3. **Secure Token Storage**
```typescript
await Preferences.set({
  key: 'auth_tokens',
  value: JSON.stringify({
    accessToken: authResult.accessToken,
    idToken: authResult.idToken,
    refreshToken: authResult.refreshToken,
    expiresAt: Date.now() + (authResult.expiresIn * 1000),
    profile: authResult.profile
  })
});
```

### 4. **Deep Link Callback Handling**
```typescript
this.deepLinkListener = App.addListener('appUrlOpen', (event) => {
  this.handleAuthCallback(event.url);
});
```

## Installation & Setup

### 1. **Install Dependencies**
```bash
npm install @capacitor/inappbrowser
npx cap sync
```

### 2. **Android Configuration**
Ensure `android/app/src/main/AndroidManifest.xml` includes:
```xml
<intent-filter android:autoVerify="true">
    <action android:name="android.intent.action.VIEW" />
    <category android:name="android.intent.category.DEFAULT" />
    <category android:name="android.intent.category.BROWSABLE" />
    <data android:scheme="capacitor" android:host="localhost" />
</intent-filter>
```

### 3. **iOS Configuration**
Ensure `ios/App/App/Info.plist` includes:
```xml
<key>CFBundleURLTypes</key>
<array>
    <dict>
        <key>CFBundleURLName</key>
        <string>com.santillana.agendafamiliar</string>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>capacitor</string>
        </array>
    </dict>
</array>
```

## API Reference

### CapacitorAuthService

#### Methods

**`initialize(): Promise<void>`**
- Initializes the authentication service
- Sets up deep link listeners
- Must be called on app startup

**`signIn(): Promise<AuthResult>`**
- Starts the authentication flow
- Opens secure WebView for Santillana Connect
- Returns authentication result with tokens and profile

**`getCurrentUser(): Promise<AuthResult | null>`**
- Gets currently authenticated user
- Returns null if not authenticated or token expired
- Automatically cleans up expired tokens

**`signOut(): Promise<void>`**
- Signs out the user
- Clears all stored tokens and state
- Cleans up any pending authentication

**`isAuthInProgress(): boolean`**
- Checks if authentication is currently in progress
- Prevents multiple simultaneous auth attempts

#### Types

```typescript
interface AuthResult {
  accessToken: string;
  idToken: string;
  refreshToken?: string;
  expiresIn: number;
  profile: {
    sub: string;
    name?: string;
    email?: string;
    [key: string]: any;
  };
}

interface AuthState {
  state: string;
  codeVerifier: string;
  nonce: string;
  redirectUri: string;
  timestamp: number;
}
```

## Usage Examples

### Basic Authentication
```typescript
import { CapacitorAuthService } from './services/capacitor-auth.service';

// Initialize on app startup
await CapacitorAuthService.initialize();

// Sign in
try {
  const authResult = await CapacitorAuthService.signIn();
  console.log('User authenticated:', authResult.profile);
} catch (error) {
  console.error('Authentication failed:', error);
}

// Get current user
const user = await CapacitorAuthService.getCurrentUser();
if (user) {
  console.log('User is authenticated:', user.profile);
} else {
  console.log('User is not authenticated');
}

// Sign out
await CapacitorAuthService.signOut();
```

### Error Handling
```typescript
try {
  const authResult = await CapacitorAuthService.signIn();
  // Handle success
} catch (error) {
  if (error.message.includes('timeout')) {
    // Handle timeout
  } else if (error.message.includes('cancelled')) {
    // Handle user cancellation
  } else {
    // Handle other errors
  }
}
```

## Security Features

1. **PKCE (Proof Key for Code Exchange)** with SHA256
2. **State parameter** for CSRF protection
3. **Nonce parameter** for replay attack prevention
4. **Secure token storage** using Capacitor Preferences
5. **Automatic token expiration** handling
6. **Deep link validation** and sanitization

## Testing

### Prerequisites
```bash
# Build the app
npx cap build android
npx cap sync android
```

### Test Steps
1. **Clear existing tokens**:
   ```javascript
   await Preferences.clear();
   ```

2. **Test authentication flow**:
   ```bash
   npx cap run android
   ```

3. **Monitor logs** for successful flow:
   ```
   🔐 [AUTH] CapacitorAuthService - Initializing modern WebView authentication
   🔐 [AUTH] CapacitorAuthService - Starting secure WebView authentication
   🔐 [AUTH] CapacitorAuthService - Opening secure WebView
   🔐 [AUTH] CapacitorAuthService - Processing auth callback
   🔐 [AUTH] CapacitorAuthService - State verification passed, exchanging code for tokens
   🔐 [AUTH] CapacitorAuthService - Token exchange successful
   🔐 [AUTH] CapacitorAuthService - Auth result stored securely
   ```

## Troubleshooting

### Common Issues

1. **WebView doesn't open**: Check InAppBrowser installation
2. **Deep link not working**: Verify AndroidManifest.xml configuration
3. **Token exchange fails**: Check network connectivity and OIDC configuration
4. **State mismatch**: Clear stored preferences and try again

### Debug Commands
```javascript
// Check stored tokens
const result = await Preferences.get({ key: 'auth_tokens' });
console.log('Stored tokens:', result.value);

// Check auth states
const keys = await Preferences.keys();
console.log('All keys:', keys.keys);
```

This modern implementation provides a robust, secure, and reliable authentication system that works seamlessly across Android and iOS platforms while maintaining compatibility with Santillana Connect's OIDC requirements.
