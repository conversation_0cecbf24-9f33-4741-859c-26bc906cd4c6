![Logo](./public/img/logo.png)

# Agenda Familiar App

## Index

- [Getting Started](#getting-started)
- [Project Structure](#project-structure)

-- -

## Getting Started

### Using docker

```bash

# DEV local hot-reload
docker compose -f docker-compose.dev.yml up --build

# PRE the optimised bundle as CI
docker compose -f docker-compose.pre.yml up --build

# PROD artefact pipeline will push to a registry
docker compose -f docker-compose.prod.yml build
docker compose -f docker-compose.prod.yml up
```

## Project Structure

```
src
├── core/                           # Application-wide core functionality
│   ├── auth/                       # Authentication infrastructure
│   │   ├── adapters/               # OpenID Connect implementation
│   │   ├── hooks/                  # React hooks for auth
│   │   └── guards/                 # Route protection
│   ├── http/                       # HTTP infrastructure
│   │   ├── interceptors/           # Token injection, error handling
│   │   └── cache/                  # Caching strategies
│   ├── storage/                    # Local storage abstraction
│   └── config/                     # App configuration
│
├── domains/                        # Business domains (DDD)
│   ├── auth/                       # Authentication domain
│   │   ├── entities/               # Domain entities
│   │   │   ├── User.ts
│   │   │   └── types.ts            # Auth-specific entity types
│   │   ├── repositories/           # Repository interfaces
│   │   ├── services/               # Domain services
│   │   └── hooks/                  # Domain-specific React hooks
│   ├── calendar/                   # Calendar domain
│   │   ├── entities/
│   │       ├── Event.ts
│   │       └── types.ts
│   ├── messaging/                  # Messaging domain
│   ├── payments/                   # Payments domain
│   ├── attendance/                 # Attendance domain
│   ├── reports/                    # Academic reports domain
│   ├── resources/                  # Educational resources domain
│   └── shared/                     # Cross-domain shared models/types
│
├── infrastructure/                 # External systems integration
│   ├── api/                        # API clients
│   │   ├── calendar/               # Calendar API integration
│   │   ├── messaging/              # Messaging API integration
│   │   ├── payments/               # Payments API integration
│   │   └── reports/                # Reports API integration
│   ├── auth/                       # Auth providers (OpenID)
│   ├── storage/                    # Storage implementations
│   └── device/                     # Capacitor plugins/native features
│
├── ui/                             # UI components (Atomic Design)
│   ├── atoms/                      # Basic building blocks
│   │   ├── Button/
│   │   ├── Input/
│   │   └── Typography/
│   ├── molecules/                  # Combinations of atoms
│   │   ├── FormField/
│   │   ├── Card/
│   │   └── ListItem/
│   ├── organisms/                  # Complex UI components
│   │   ├── Header/
│   │   ├── Calendar/
│   │   └── MessageList/
│   ├── templates/                  # Page layouts
│   │   ├── MainLayout/
│   │   └── AuthLayout/
│   └── theme/                      # Global styling
│
├── pages/                          # Application pages
│   ├── auth/                       # Auth pages
│   │   ├── LoginPage/
│   │   └── PasswordResetPage/
│   ├── calendar/                   # Calendar pages
│   ├── messaging/                  # Messaging pages
│   ├── payments/                   # Payments pages
│   ├── reports/                    # Reports pages
│   ├── resources/                  # Resources pages
│   └── profile/                    # User profile pages
│
├── utils/                          # Utility functions
│   ├── formatters/                 # Date, number formatting
│   ├── validators/                 # Form validation
│   └── hooks/                      # Shared React hooks
│
├── routes/                         # Routing configuration
│   ├── AppRoutes.tsx               # Main routes definition
│   ├── RouteGuard.tsx              # Route protection component
│   └── routes.ts                   # Routes constants
│
├── tests/                          # Test files
│   ├── unit/                       # Unit tests
│   ├── integration/                # Integration tests
│   ├── e2e/                        # End-to-end tests
│   └── mocks/                      # Test mocks and fixtures
│
├── App.tsx                         # Root component
├── index.tsx                       # Entry point
├── vite-env.d.ts                   # TypeScript declarations
└── env.d.ts                        # Environment variables typing
```

